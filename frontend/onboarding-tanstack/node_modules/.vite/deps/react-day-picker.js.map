{"version": 3, "sources": ["../../react-day-picker/src/DayPicker.tsx", "../../@date-fns/tz/constants/index.js", "../../@date-fns/tz/tzOffset/index.js", "../../@date-fns/tz/date/mini.js", "../../@date-fns/tz/date/index.js", "../../react-day-picker/src/UI.ts", "../../react-day-picker/src/helpers/getBroadcastWeeksInMonth.ts", "../../react-day-picker/src/helpers/startOfBroadcastWeek.ts", "../../react-day-picker/src/helpers/endOfBroadcastWeek.ts", "../../react-day-picker/src/classes/DateLib.ts", "../../react-day-picker/src/classes/CalendarDay.ts", "../../react-day-picker/src/classes/CalendarMonth.ts", "../../react-day-picker/src/classes/CalendarWeek.ts", "../../react-day-picker/src/utils/rangeIncludesDate.ts", "../../react-day-picker/src/utils/typeguards.ts", "../../react-day-picker/src/utils/dateMatchModifiers.ts", "../../react-day-picker/src/helpers/createGetModifiers.ts", "../../react-day-picker/src/helpers/getClassNamesForModifiers.ts", "../../react-day-picker/src/components/custom-components.tsx", "../../react-day-picker/src/components/Button.tsx", "../../react-day-picker/src/components/CaptionLabel.tsx", "../../react-day-picker/src/components/Chevron.tsx", "../../react-day-picker/src/components/Day.tsx", "../../react-day-picker/src/components/DayButton.tsx", "../../react-day-picker/src/components/Dropdown.tsx", "../../react-day-picker/src/components/DropdownNav.tsx", "../../react-day-picker/src/components/Footer.tsx", "../../react-day-picker/src/components/Month.tsx", "../../react-day-picker/src/components/MonthCaption.tsx", "../../react-day-picker/src/components/MonthGrid.tsx", "../../react-day-picker/src/components/Months.tsx", "../../react-day-picker/src/components/MonthsDropdown.tsx", "../../react-day-picker/src/useDayPicker.ts", "../../react-day-picker/src/components/Nav.tsx", "../../react-day-picker/src/components/NextMonthButton.tsx", "../../react-day-picker/src/components/Option.tsx", "../../react-day-picker/src/components/PreviousMonthButton.tsx", "../../react-day-picker/src/components/Root.tsx", "../../react-day-picker/src/components/Select.tsx", "../../react-day-picker/src/components/Week.tsx", "../../react-day-picker/src/components/Weekday.tsx", "../../react-day-picker/src/components/Weekdays.tsx", "../../react-day-picker/src/components/WeekNumber.tsx", "../../react-day-picker/src/components/WeekNumberHeader.tsx", "../../react-day-picker/src/components/Weeks.tsx", "../../react-day-picker/src/components/YearsDropdown.tsx", "../../react-day-picker/src/helpers/getComponents.ts", "../../react-day-picker/src/helpers/getDataAttributes.tsx", "../../react-day-picker/src/helpers/getDefaultClassNames.ts", "../../react-day-picker/src/formatters/index.ts", "../../react-day-picker/src/formatters/formatCaption.ts", "../../react-day-picker/src/formatters/formatDay.ts", "../../react-day-picker/src/formatters/formatMonthDropdown.ts", "../../react-day-picker/src/formatters/formatWeekNumber.ts", "../../react-day-picker/src/formatters/formatWeekNumberHeader.ts", "../../react-day-picker/src/formatters/formatWeekdayName.ts", "../../react-day-picker/src/formatters/formatYearDropdown.ts", "../../react-day-picker/src/helpers/getFormatters.ts", "../../react-day-picker/src/helpers/getMonthOptions.ts", "../../react-day-picker/src/helpers/getStyleForModifiers.ts", "../../react-day-picker/src/helpers/getWeekdays.ts", "../../react-day-picker/src/helpers/getYearOptions.ts", "../../react-day-picker/src/labels/index.ts", "../../react-day-picker/src/labels/labelGrid.ts", "../../react-day-picker/src/labels/labelGridcell.ts", "../../react-day-picker/src/labels/labelDayButton.ts", "../../react-day-picker/src/labels/labelNav.ts", "../../react-day-picker/src/labels/labelMonthDropdown.ts", "../../react-day-picker/src/labels/labelNext.ts", "../../react-day-picker/src/labels/labelPrevious.ts", "../../react-day-picker/src/labels/labelWeekday.ts", "../../react-day-picker/src/labels/labelWeekNumber.ts", "../../react-day-picker/src/labels/labelWeekNumberHeader.ts", "../../react-day-picker/src/labels/labelYearDropdown.ts", "../../react-day-picker/src/useAnimation.ts", "../../react-day-picker/src/useCalendar.ts", "../../react-day-picker/src/helpers/getDates.ts", "../../react-day-picker/src/helpers/getDays.ts", "../../react-day-picker/src/helpers/getDisplayMonths.ts", "../../react-day-picker/src/helpers/getInitialMonth.ts", "../../react-day-picker/src/helpers/getMonths.ts", "../../react-day-picker/src/helpers/getNavMonth.ts", "../../react-day-picker/src/helpers/getNextMonth.ts", "../../react-day-picker/src/helpers/getPreviousMonth.ts", "../../react-day-picker/src/helpers/getWeeks.ts", "../../react-day-picker/src/helpers/useControlledValue.ts", "../../react-day-picker/src/useFocus.ts", "../../react-day-picker/src/helpers/calculateFocusTarget.ts", "../../react-day-picker/src/helpers/getFocusableDate.ts", "../../react-day-picker/src/helpers/getNextFocus.tsx", "../../react-day-picker/src/selection/useMulti.tsx", "../../react-day-picker/src/utils/addToRange.ts", "../../react-day-picker/src/utils/rangeContainsDayOfWeek.ts", "../../react-day-picker/src/utils/rangeOverlaps.ts", "../../react-day-picker/src/utils/rangeContainsModifiers.ts", "../../react-day-picker/src/selection/useRange.tsx", "../../react-day-picker/src/selection/useSingle.tsx", "../../react-day-picker/src/useSelection.ts", "../../react-day-picker/src/types/deprecated.ts"], "sourcesContent": ["import React, { use<PERSON><PERSON>back, useMemo, useRef } from \"react\";\nimport type { MouseEvent, FocusEvent, KeyboardEvent, ChangeEvent } from \"react\";\n\nimport { TZDate } from \"@date-fns/tz\";\n\nimport { UI, DayFlag, SelectionState } from \"./UI.js\";\nimport type { CalendarDay } from \"./classes/CalendarDay.js\";\nimport { DateLib, defaultLocale } from \"./classes/DateLib.js\";\nimport { createGetModifiers } from \"./helpers/createGetModifiers.js\";\nimport { getClassNamesForModifiers } from \"./helpers/getClassNamesForModifiers.js\";\nimport { getComponents } from \"./helpers/getComponents.js\";\nimport { getDataAttributes } from \"./helpers/getDataAttributes.js\";\nimport { getDefaultClassNames } from \"./helpers/getDefaultClassNames.js\";\nimport { getFormatters } from \"./helpers/getFormatters.js\";\nimport { getMonthOptions } from \"./helpers/getMonthOptions.js\";\nimport { getStyleForModifiers } from \"./helpers/getStyleForModifiers.js\";\nimport { getWeekdays } from \"./helpers/getWeekdays.js\";\nimport { getYearOptions } from \"./helpers/getYearOptions.js\";\nimport * as defaultLabels from \"./labels/index.js\";\nimport type {\n  DayPickerProps,\n  Modifiers,\n  MoveFocusBy,\n  MoveFocusDir,\n  SelectedValue,\n  SelectHandler\n} from \"./types/index.js\";\nimport { useAnimation } from \"./useAnimation.js\";\nimport { useCalendar } from \"./useCalendar.js\";\nimport { type DayPickerContext, dayPickerContext } from \"./useDayPicker.js\";\nimport { useFocus } from \"./useFocus.js\";\nimport { useSelection } from \"./useSelection.js\";\nimport { rangeIncludesDate } from \"./utils/rangeIncludesDate.js\";\nimport { isDateRange } from \"./utils/typeguards.js\";\n\n/**\n * Renders the DayPicker calendar component.\n *\n * @param initialProps - The props for the DayPicker component.\n * @returns The rendered DayPicker component.\n * @group DayPicker\n * @see https://daypicker.dev\n */\nexport function DayPicker(initialProps: DayPickerProps) {\n  let props = initialProps;\n\n  if (props.timeZone) {\n    props = {\n      ...initialProps\n    };\n    if (props.today) {\n      props.today = new TZDate(props.today, props.timeZone);\n    }\n    if (props.month) {\n      props.month = new TZDate(props.month, props.timeZone);\n    }\n    if (props.defaultMonth) {\n      props.defaultMonth = new TZDate(props.defaultMonth, props.timeZone);\n    }\n    if (props.startMonth) {\n      props.startMonth = new TZDate(props.startMonth, props.timeZone);\n    }\n    if (props.endMonth) {\n      props.endMonth = new TZDate(props.endMonth, props.timeZone);\n    }\n    if (props.mode === \"single\" && props.selected) {\n      props.selected = new TZDate(props.selected, props.timeZone);\n    } else if (props.mode === \"multiple\" && props.selected) {\n      props.selected = props.selected?.map(\n        (date) => new TZDate(date, props.timeZone)\n      );\n    } else if (props.mode === \"range\" && props.selected) {\n      props.selected = {\n        from: props.selected.from\n          ? new TZDate(props.selected.from, props.timeZone)\n          : undefined,\n        to: props.selected.to\n          ? new TZDate(props.selected.to, props.timeZone)\n          : undefined\n      };\n    }\n  }\n  const { components, formatters, labels, dateLib, locale, classNames } =\n    useMemo(() => {\n      const locale = { ...defaultLocale, ...props.locale };\n\n      const dateLib = new DateLib(\n        {\n          locale,\n          weekStartsOn: props.broadcastCalendar ? 1 : props.weekStartsOn,\n          firstWeekContainsDate: props.firstWeekContainsDate,\n          useAdditionalWeekYearTokens: props.useAdditionalWeekYearTokens,\n          useAdditionalDayOfYearTokens: props.useAdditionalDayOfYearTokens,\n          timeZone: props.timeZone,\n          numerals: props.numerals\n        },\n        props.dateLib\n      );\n\n      return {\n        dateLib,\n        components: getComponents(props.components),\n        formatters: getFormatters(props.formatters),\n        labels: { ...defaultLabels, ...props.labels },\n        locale,\n        classNames: { ...getDefaultClassNames(), ...props.classNames }\n      };\n    }, [\n      props.locale,\n      props.broadcastCalendar,\n      props.weekStartsOn,\n      props.firstWeekContainsDate,\n      props.useAdditionalWeekYearTokens,\n      props.useAdditionalDayOfYearTokens,\n      props.timeZone,\n      props.numerals,\n      props.dateLib,\n      props.components,\n      props.formatters,\n      props.labels,\n      props.classNames\n    ]);\n\n  const {\n    captionLayout,\n    mode,\n    navLayout,\n    numberOfMonths = 1,\n    onDayBlur,\n    onDayClick,\n    onDayFocus,\n    onDayKeyDown,\n    onDayMouseEnter,\n    onDayMouseLeave,\n    onNextClick,\n    onPrevClick,\n    showWeekNumber,\n    styles\n  } = props;\n\n  const {\n    formatCaption,\n    formatDay,\n    formatMonthDropdown,\n    formatWeekNumber,\n    formatWeekNumberHeader,\n    formatWeekdayName,\n    formatYearDropdown\n  } = formatters;\n\n  const calendar = useCalendar(props, dateLib);\n\n  const {\n    days,\n    months,\n    navStart,\n    navEnd,\n    previousMonth,\n    nextMonth,\n    goToMonth\n  } = calendar;\n\n  const getModifiers = createGetModifiers(\n    days,\n    props,\n    navStart,\n    navEnd,\n    dateLib\n  );\n\n  const {\n    isSelected,\n    select,\n    selected: selectedValue\n  } = useSelection(props, dateLib) ?? {};\n\n  const { blur, focused, isFocusTarget, moveFocus, setFocused } = useFocus(\n    props,\n    calendar,\n    getModifiers,\n    isSelected ?? (() => false),\n    dateLib\n  );\n\n  const {\n    labelDayButton,\n    labelGridcell,\n    labelGrid,\n    labelMonthDropdown,\n    labelNav,\n    labelPrevious,\n    labelNext,\n    labelWeekday,\n    labelWeekNumber,\n    labelWeekNumberHeader,\n    labelYearDropdown\n  } = labels;\n\n  const weekdays = useMemo(\n    () => getWeekdays(dateLib, props.ISOWeek),\n    [dateLib, props.ISOWeek]\n  );\n\n  const isInteractive = mode !== undefined || onDayClick !== undefined;\n\n  const handlePreviousClick = useCallback(() => {\n    if (!previousMonth) return;\n    goToMonth(previousMonth);\n    onPrevClick?.(previousMonth);\n  }, [previousMonth, goToMonth, onPrevClick]);\n\n  const handleNextClick = useCallback(() => {\n    if (!nextMonth) return;\n    goToMonth(nextMonth);\n    onNextClick?.(nextMonth);\n  }, [goToMonth, nextMonth, onNextClick]);\n\n  const handleDayClick = useCallback(\n    (day: CalendarDay, m: Modifiers) => (e: MouseEvent) => {\n      e.preventDefault();\n      e.stopPropagation();\n      setFocused(day);\n      select?.(day.date, m, e);\n      onDayClick?.(day.date, m, e);\n    },\n    [select, onDayClick, setFocused]\n  );\n\n  const handleDayFocus = useCallback(\n    (day: CalendarDay, m: Modifiers) => (e: FocusEvent) => {\n      setFocused(day);\n      onDayFocus?.(day.date, m, e);\n    },\n    [onDayFocus, setFocused]\n  );\n\n  const handleDayBlur = useCallback(\n    (day: CalendarDay, m: Modifiers) => (e: FocusEvent) => {\n      blur();\n      onDayBlur?.(day.date, m, e);\n    },\n    [blur, onDayBlur]\n  );\n\n  const handleDayKeyDown = useCallback(\n    (day: CalendarDay, modifiers: Modifiers) => (e: KeyboardEvent) => {\n      const keyMap: Record<string, [MoveFocusBy, MoveFocusDir]> = {\n        ArrowLeft: [\n          e.shiftKey ? \"month\" : \"day\",\n          props.dir === \"rtl\" ? \"after\" : \"before\"\n        ],\n        ArrowRight: [\n          e.shiftKey ? \"month\" : \"day\",\n          props.dir === \"rtl\" ? \"before\" : \"after\"\n        ],\n        ArrowDown: [e.shiftKey ? \"year\" : \"week\", \"after\"],\n        ArrowUp: [e.shiftKey ? \"year\" : \"week\", \"before\"],\n        PageUp: [e.shiftKey ? \"year\" : \"month\", \"before\"],\n        PageDown: [e.shiftKey ? \"year\" : \"month\", \"after\"],\n        Home: [\"startOfWeek\", \"before\"],\n        End: [\"endOfWeek\", \"after\"]\n      };\n      if (keyMap[e.key]) {\n        e.preventDefault();\n        e.stopPropagation();\n        const [moveBy, moveDir] = keyMap[e.key];\n        moveFocus(moveBy, moveDir);\n      }\n      onDayKeyDown?.(day.date, modifiers, e);\n    },\n    [moveFocus, onDayKeyDown, props.dir]\n  );\n\n  const handleDayMouseEnter = useCallback(\n    (day: CalendarDay, modifiers: Modifiers) => (e: MouseEvent) => {\n      onDayMouseEnter?.(day.date, modifiers, e);\n    },\n    [onDayMouseEnter]\n  );\n\n  const handleDayMouseLeave = useCallback(\n    (day: CalendarDay, modifiers: Modifiers) => (e: MouseEvent) => {\n      onDayMouseLeave?.(day.date, modifiers, e);\n    },\n    [onDayMouseLeave]\n  );\n\n  const handleMonthChange = useCallback(\n    (date: Date) => (e: ChangeEvent<HTMLSelectElement>) => {\n      const selectedMonth = Number(e.target.value);\n      const month = dateLib.setMonth(dateLib.startOfMonth(date), selectedMonth);\n      goToMonth(month);\n    },\n    [dateLib, goToMonth]\n  );\n\n  const handleYearChange = useCallback(\n    (date: Date) => (e: ChangeEvent<HTMLSelectElement>) => {\n      const selectedYear = Number(e.target.value);\n      const month = dateLib.setYear(dateLib.startOfMonth(date), selectedYear);\n      goToMonth(month);\n    },\n    [dateLib, goToMonth]\n  );\n\n  const { className, style } = useMemo(\n    () => ({\n      className: [classNames[UI.Root], props.className]\n        .filter(Boolean)\n        .join(\" \"),\n      style: { ...styles?.[UI.Root], ...props.style }\n    }),\n    [classNames, props.className, props.style, styles]\n  );\n\n  const dataAttributes = getDataAttributes(props);\n\n  const rootElRef = useRef<HTMLDivElement>(null);\n  useAnimation(rootElRef, Boolean(props.animate), {\n    classNames,\n    months,\n    focused,\n    dateLib\n  });\n\n  const contextValue: DayPickerContext<DayPickerProps> = {\n    dayPickerProps: props,\n    selected: selectedValue as SelectedValue<DayPickerProps>,\n    select: select as SelectHandler<DayPickerProps>,\n    isSelected,\n    months,\n    nextMonth,\n    previousMonth,\n    goToMonth,\n    getModifiers,\n    components,\n    classNames,\n    styles,\n    labels,\n    formatters\n  };\n\n  return (\n    <dayPickerContext.Provider value={contextValue}>\n      <components.Root\n        rootRef={props.animate ? rootElRef : undefined}\n        className={className}\n        style={style}\n        dir={props.dir}\n        id={props.id}\n        lang={props.lang}\n        nonce={props.nonce}\n        title={props.title}\n        role={props.role}\n        aria-label={props[\"aria-label\"]}\n        {...dataAttributes}\n      >\n        <components.Months\n          className={classNames[UI.Months]}\n          style={styles?.[UI.Months]}\n        >\n          {!props.hideNavigation && !navLayout && (\n            <components.Nav\n              data-animated-nav={props.animate ? \"true\" : undefined}\n              className={classNames[UI.Nav]}\n              style={styles?.[UI.Nav]}\n              aria-label={labelNav()}\n              onPreviousClick={handlePreviousClick}\n              onNextClick={handleNextClick}\n              previousMonth={previousMonth}\n              nextMonth={nextMonth}\n            />\n          )}\n          {months.map((calendarMonth, displayIndex) => {\n            const dropdownMonths = getMonthOptions(\n              calendarMonth.date,\n              navStart,\n              navEnd,\n              formatters,\n              dateLib\n            );\n\n            const dropdownYears = getYearOptions(\n              navStart,\n              navEnd,\n              formatters,\n              dateLib\n            );\n            return (\n              <components.Month\n                data-animated-month={props.animate ? \"true\" : undefined}\n                className={classNames[UI.Month]}\n                style={styles?.[UI.Month]}\n                key={displayIndex}\n                displayIndex={displayIndex}\n                calendarMonth={calendarMonth}\n              >\n                {navLayout === \"around\" &&\n                  !props.hideNavigation &&\n                  displayIndex === 0 && (\n                    <components.PreviousMonthButton\n                      type=\"button\"\n                      className={classNames[UI.PreviousMonthButton]}\n                      tabIndex={previousMonth ? undefined : -1}\n                      aria-disabled={previousMonth ? undefined : true}\n                      aria-label={labelPrevious(previousMonth)}\n                      onClick={handlePreviousClick}\n                      data-animated-button={props.animate ? \"true\" : undefined}\n                    >\n                      <components.Chevron\n                        disabled={previousMonth ? undefined : true}\n                        className={classNames[UI.Chevron]}\n                        orientation={props.dir === \"rtl\" ? \"right\" : \"left\"}\n                      />\n                    </components.PreviousMonthButton>\n                  )}\n                <components.MonthCaption\n                  data-animated-caption={props.animate ? \"true\" : undefined}\n                  className={classNames[UI.MonthCaption]}\n                  style={styles?.[UI.MonthCaption]}\n                  calendarMonth={calendarMonth}\n                  displayIndex={displayIndex}\n                >\n                  {captionLayout?.startsWith(\"dropdown\") ? (\n                    <components.DropdownNav\n                      className={classNames[UI.Dropdowns]}\n                      style={styles?.[UI.Dropdowns]}\n                    >\n                      {captionLayout === \"dropdown\" ||\n                      captionLayout === \"dropdown-months\" ? (\n                        <components.MonthsDropdown\n                          className={classNames[UI.MonthsDropdown]}\n                          aria-label={labelMonthDropdown()}\n                          classNames={classNames}\n                          components={components}\n                          disabled={Boolean(props.disableNavigation)}\n                          onChange={handleMonthChange(calendarMonth.date)}\n                          options={dropdownMonths}\n                          style={styles?.[UI.Dropdown]}\n                          value={dateLib.getMonth(calendarMonth.date)}\n                        />\n                      ) : (\n                        <span>\n                          {formatMonthDropdown(calendarMonth.date, dateLib)}\n                        </span>\n                      )}\n                      {captionLayout === \"dropdown\" ||\n                      captionLayout === \"dropdown-years\" ? (\n                        <components.YearsDropdown\n                          className={classNames[UI.YearsDropdown]}\n                          aria-label={labelYearDropdown(dateLib.options)}\n                          classNames={classNames}\n                          components={components}\n                          disabled={Boolean(props.disableNavigation)}\n                          onChange={handleYearChange(calendarMonth.date)}\n                          options={dropdownYears}\n                          style={styles?.[UI.Dropdown]}\n                          value={dateLib.getYear(calendarMonth.date)}\n                        />\n                      ) : (\n                        <span>\n                          {formatYearDropdown(calendarMonth.date, dateLib)}\n                        </span>\n                      )}\n                      <span\n                        role=\"status\"\n                        aria-live=\"polite\"\n                        style={{\n                          border: 0,\n                          clip: \"rect(0 0 0 0)\",\n                          height: \"1px\",\n                          margin: \"-1px\",\n                          overflow: \"hidden\",\n                          padding: 0,\n                          position: \"absolute\",\n                          width: \"1px\",\n                          whiteSpace: \"nowrap\",\n                          wordWrap: \"normal\"\n                        }}\n                      >\n                        {formatCaption(\n                          calendarMonth.date,\n                          dateLib.options,\n                          dateLib\n                        )}\n                      </span>\n                    </components.DropdownNav>\n                  ) : (\n                    <components.CaptionLabel\n                      className={classNames[UI.CaptionLabel]}\n                      role=\"status\"\n                      aria-live=\"polite\"\n                    >\n                      {formatCaption(\n                        calendarMonth.date,\n                        dateLib.options,\n                        dateLib\n                      )}\n                    </components.CaptionLabel>\n                  )}\n                </components.MonthCaption>\n                {navLayout === \"around\" &&\n                  !props.hideNavigation &&\n                  displayIndex === numberOfMonths - 1 && (\n                    <components.NextMonthButton\n                      type=\"button\"\n                      className={classNames[UI.NextMonthButton]}\n                      tabIndex={nextMonth ? undefined : -1}\n                      aria-disabled={nextMonth ? undefined : true}\n                      aria-label={labelNext(nextMonth)}\n                      onClick={handleNextClick}\n                      data-animated-button={props.animate ? \"true\" : undefined}\n                    >\n                      <components.Chevron\n                        disabled={nextMonth ? undefined : true}\n                        className={classNames[UI.Chevron]}\n                        orientation={props.dir === \"rtl\" ? \"left\" : \"right\"}\n                      />\n                    </components.NextMonthButton>\n                  )}\n                {displayIndex === numberOfMonths - 1 &&\n                  navLayout === \"after\" &&\n                  !props.hideNavigation && (\n                    <components.Nav\n                      data-animated-nav={props.animate ? \"true\" : undefined}\n                      className={classNames[UI.Nav]}\n                      style={styles?.[UI.Nav]}\n                      aria-label={labelNav()}\n                      onPreviousClick={handlePreviousClick}\n                      onNextClick={handleNextClick}\n                      previousMonth={previousMonth}\n                      nextMonth={nextMonth}\n                    />\n                  )}\n\n                <components.MonthGrid\n                  role=\"grid\"\n                  aria-multiselectable={mode === \"multiple\" || mode === \"range\"}\n                  aria-label={\n                    labelGrid(calendarMonth.date, dateLib.options, dateLib) ||\n                    undefined\n                  }\n                  className={classNames[UI.MonthGrid]}\n                  style={styles?.[UI.MonthGrid]}\n                >\n                  {!props.hideWeekdays && (\n                    <components.Weekdays\n                      data-animated-weekdays={\n                        props.animate ? \"true\" : undefined\n                      }\n                      className={classNames[UI.Weekdays]}\n                      style={styles?.[UI.Weekdays]}\n                    >\n                      {showWeekNumber && (\n                        <components.WeekNumberHeader\n                          aria-label={labelWeekNumberHeader(dateLib.options)}\n                          className={classNames[UI.WeekNumberHeader]}\n                          style={styles?.[UI.WeekNumberHeader]}\n                          scope=\"col\"\n                        >\n                          {formatWeekNumberHeader()}\n                        </components.WeekNumberHeader>\n                      )}\n                      {weekdays.map((weekday, i) => (\n                        <components.Weekday\n                          aria-label={labelWeekday(\n                            weekday,\n                            dateLib.options,\n                            dateLib\n                          )}\n                          className={classNames[UI.Weekday]}\n                          key={i}\n                          style={styles?.[UI.Weekday]}\n                          scope=\"col\"\n                        >\n                          {formatWeekdayName(weekday, dateLib.options, dateLib)}\n                        </components.Weekday>\n                      ))}\n                    </components.Weekdays>\n                  )}\n                  <components.Weeks\n                    data-animated-weeks={props.animate ? \"true\" : undefined}\n                    className={classNames[UI.Weeks]}\n                    style={styles?.[UI.Weeks]}\n                  >\n                    {calendarMonth.weeks.map((week, weekIndex) => {\n                      return (\n                        <components.Week\n                          className={classNames[UI.Week]}\n                          key={week.weekNumber}\n                          style={styles?.[UI.Week]}\n                          week={week}\n                        >\n                          {showWeekNumber && (\n                            <components.WeekNumber\n                              week={week}\n                              style={styles?.[UI.WeekNumber]}\n                              aria-label={labelWeekNumber(week.weekNumber, {\n                                locale\n                              })}\n                              className={classNames[UI.WeekNumber]}\n                              scope=\"row\"\n                              role=\"rowheader\"\n                            >\n                              {formatWeekNumber(week.weekNumber, dateLib)}\n                            </components.WeekNumber>\n                          )}\n                          {week.days.map((day: CalendarDay) => {\n                            const { date } = day;\n                            const modifiers = getModifiers(day);\n\n                            modifiers[DayFlag.focused] =\n                              !modifiers.hidden &&\n                              Boolean(focused?.isEqualTo(day));\n\n                            modifiers[SelectionState.selected] =\n                              isSelected?.(date) || modifiers.selected;\n\n                            if (isDateRange(selectedValue)) {\n                              // add range modifiers\n                              const { from, to } = selectedValue;\n                              modifiers[SelectionState.range_start] = Boolean(\n                                from && to && dateLib.isSameDay(date, from)\n                              );\n                              modifiers[SelectionState.range_end] = Boolean(\n                                from && to && dateLib.isSameDay(date, to)\n                              );\n                              modifiers[SelectionState.range_middle] =\n                                rangeIncludesDate(\n                                  selectedValue,\n                                  date,\n                                  true,\n                                  dateLib\n                                );\n                            }\n\n                            const style = getStyleForModifiers(\n                              modifiers,\n                              styles,\n                              props.modifiersStyles\n                            );\n\n                            const className = getClassNamesForModifiers(\n                              modifiers,\n                              classNames,\n                              props.modifiersClassNames\n                            );\n\n                            const ariaLabel =\n                              !isInteractive && !modifiers.hidden\n                                ? labelGridcell(\n                                    date,\n                                    modifiers,\n                                    dateLib.options,\n                                    dateLib\n                                  )\n                                : undefined;\n\n                            return (\n                              <components.Day\n                                key={`${dateLib.format(date, \"yyyy-MM-dd\")}_${dateLib.format(day.displayMonth, \"yyyy-MM\")}`}\n                                day={day}\n                                modifiers={modifiers}\n                                className={className.join(\" \")}\n                                style={style}\n                                role=\"gridcell\"\n                                aria-selected={modifiers.selected || undefined}\n                                aria-label={ariaLabel}\n                                data-day={dateLib.format(date, \"yyyy-MM-dd\")}\n                                data-month={\n                                  day.outside\n                                    ? dateLib.format(date, \"yyyy-MM\")\n                                    : undefined\n                                }\n                                data-selected={modifiers.selected || undefined}\n                                data-disabled={modifiers.disabled || undefined}\n                                data-hidden={modifiers.hidden || undefined}\n                                data-outside={day.outside || undefined}\n                                data-focused={modifiers.focused || undefined}\n                                data-today={modifiers.today || undefined}\n                              >\n                                {!modifiers.hidden && isInteractive ? (\n                                  <components.DayButton\n                                    className={classNames[UI.DayButton]}\n                                    style={styles?.[UI.DayButton]}\n                                    type=\"button\"\n                                    day={day}\n                                    modifiers={modifiers}\n                                    disabled={modifiers.disabled || undefined}\n                                    tabIndex={isFocusTarget(day) ? 0 : -1}\n                                    aria-label={labelDayButton(\n                                      date,\n                                      modifiers,\n                                      dateLib.options,\n                                      dateLib\n                                    )}\n                                    onClick={handleDayClick(day, modifiers)}\n                                    onBlur={handleDayBlur(day, modifiers)}\n                                    onFocus={handleDayFocus(day, modifiers)}\n                                    onKeyDown={handleDayKeyDown(day, modifiers)}\n                                    onMouseEnter={handleDayMouseEnter(\n                                      day,\n                                      modifiers\n                                    )}\n                                    onMouseLeave={handleDayMouseLeave(\n                                      day,\n                                      modifiers\n                                    )}\n                                  >\n                                    {formatDay(date, dateLib.options, dateLib)}\n                                  </components.DayButton>\n                                ) : (\n                                  !modifiers.hidden &&\n                                  formatDay(day.date, dateLib.options, dateLib)\n                                )}\n                              </components.Day>\n                            );\n                          })}\n                        </components.Week>\n                      );\n                    })}\n                  </components.Weeks>\n                </components.MonthGrid>\n              </components.Month>\n            );\n          })}\n        </components.Months>\n        {props.footer && (\n          <components.Footer\n            className={classNames[UI.Footer]}\n            style={styles?.[UI.Footer]}\n            role=\"status\"\n            aria-live=\"polite\"\n          >\n            {props.footer}\n          </components.Footer>\n        )}\n      </components.Root>\n    </dayPickerContext.Provider>\n  );\n}\n", "/**\n * The symbol to access the `TZDate`'s function to construct a new instance from\n * the provided value. It helps date-fns to inherit the time zone.\n */\nexport const constructFromSymbol = Symbol.for(\"constructDateFrom\");", "const offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nexport function tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-GB\", {\n      timeZone,\n      hour: \"numeric\",\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split('GMT')[1] || '';\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +values[0];\n  const minutes = +(values[1] || 0);\n  return offsetCache[cacheStr] = hours > 0 ? hours * 60 + minutes : hours * 60 - minutes;\n}", "import { tzOffset } from \"../tzOffset/index.js\";\nexport class TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN(tzOffset(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    return -tzOffset(this.timeZone, this);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCMinutes(date.internal.getUTCMinutes() - date.getTimezoneOffset());\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const offset = tzOffset(date.timeZone, date);\n\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postOffset = tzOffset(date.timeZone, date);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newOffset = tzOffset(date.timeZone, date);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}", "import { TZDateMini } from \"./mini.js\";\n\n/**\n * UTC date class. It maps getters and setters to corresponding UTC methods,\n * forcing all calculations in the UTC time zone.\n *\n * Combined with date-fns, it allows using the class the same way as\n * the original date class.\n *\n * This complete version provides not only getters, setters,\n * and `getTimezoneOffset`, but also the formatter functions, mirroring\n * all original `Date` functionality. Use this version when you need to format\n * a string or in an environment you don't fully control (a library).\n * For a minimal version, see `UTCDateMini`.\n */\nexport class TZDate extends TZDateMini {\n  //#region static\n\n  static tz(tz, ...args) {\n    return args.length ? new TZDate(...args, tz) : new TZDate(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region representation\n\n  toISOString() {\n    const [sign, hours, minutes] = this.tzComponents();\n    const tz = `${sign}${hours}:${minutes}`;\n    return this.internal.toISOString().slice(0, -1) + tz;\n  }\n  toString() {\n    // \"Tue Aug 13 2024 07:50:19 GMT+0800 (Singapore Standard Time)\";\n    return `${this.toDateString()} ${this.toTimeString()}`;\n  }\n  toDateString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const [day, date, month, year] = this.internal.toUTCString().split(\" \");\n    // \"Tue Aug 13 2024\"\n    return `${day?.slice(0, -1) /* Remove \",\" */} ${month} ${date} ${year}`;\n  }\n  toTimeString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const time = this.internal.toUTCString().split(\" \")[4];\n    const [sign, hours, minutes] = this.tzComponents();\n    // \"07:42:23 GMT+0800 (Singapore Standard Time)\"\n    return `${time} GMT${sign}${hours}${minutes} (${tzName(this.timeZone, this)})`;\n  }\n  toLocaleString(locales, options) {\n    return Date.prototype.toLocaleString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleDateString(locales, options) {\n    return Date.prototype.toLocaleDateString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleTimeString(locales, options) {\n    return Date.prototype.toLocaleTimeString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n\n  //#endregion\n\n  //#region private\n\n  tzComponents() {\n    const offset = this.getTimezoneOffset();\n    const sign = offset > 0 ? \"-\" : \"+\";\n    const hours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, \"0\");\n    const minutes = String(Math.abs(offset) % 60).padStart(2, \"0\");\n    return [sign, hours, minutes];\n  }\n\n  //#endregion\n\n  withTimeZone(timeZone) {\n    return new TZDate(+this, timeZone);\n  }\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDate(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\nfunction tzName(tz, date) {\n  return new Intl.DateTimeFormat(\"en-GB\", {\n    timeZone: tz,\n    timeZoneName: \"long\"\n  }).format(date).slice(12);\n}", "import { CSSProperties } from \"react\";\n\nimport type { CustomComponents, ClassNames, Styles } from \"./types/index.js\";\n\n/**\n * Enum representing the UI elements composing DayPicker. These elements are\n * mapped to {@link CustomComponents}, {@link ClassNames}, and {@link Styles}.\n *\n * Some elements are extended by flags and modifiers.\n */\nexport enum UI {\n  /** The root component displaying the months and the navigation bar. */\n  Root = \"root\",\n  /** The Chevron SVG element used by navigation buttons and dropdowns. */\n  Chevron = \"chevron\",\n  /**\n   * The grid cell with the day's date. Extended by {@link DayFlag} and\n   * {@link SelectionState}.\n   */\n  Day = \"day\",\n  /** The button containing the formatted day's date, inside the grid cell. */\n  DayButton = \"day_button\",\n  /** The caption label of the month (when not showing the dropdown navigation). */\n  CaptionLabel = \"caption_label\",\n  /** The container of the dropdown navigation (when enabled). */\n  Dropdowns = \"dropdowns\",\n  /** The dropdown element to select for years and months. */\n  Dropdown = \"dropdown\",\n  /** The container element of the dropdown. */\n  DropdownRoot = \"dropdown_root\",\n  /** The root element of the footer. */\n  Footer = \"footer\",\n  /** The month grid. */\n  MonthGrid = \"month_grid\",\n  /** Contains the dropdown navigation or the caption label. */\n  MonthCaption = \"month_caption\",\n  /** The dropdown with the months. */\n  MonthsDropdown = \"months_dropdown\",\n  /** Wrapper of the month grid. */\n  Month = \"month\",\n  /** The container of the displayed months. */\n  Months = \"months\",\n  /** The navigation bar with the previous and next buttons. */\n  Nav = \"nav\",\n  /**\n   * The next month button in the navigation. *\n   *\n   * @since 9.1.0\n   */\n  NextMonthButton = \"button_next\",\n  /**\n   * The previous month button in the navigation.\n   *\n   * @since 9.1.0\n   */\n  PreviousMonthButton = \"button_previous\",\n  /** The row containing the week. */\n  Week = \"week\",\n  /** The group of row weeks in a month (`tbody`). */\n  Weeks = \"weeks\",\n  /** The column header with the weekday. */\n  Weekday = \"weekday\",\n  /** The row grouping the weekdays in the column headers. */\n  Weekdays = \"weekdays\",\n  /** The cell containing the week number. */\n  WeekNumber = \"week_number\",\n  /** The cell header of the week numbers column. */\n  WeekNumberHeader = \"week_number_header\",\n  /** The dropdown with the years. */\n  YearsDropdown = \"years_dropdown\"\n}\n\n/** Enum representing flags for the {@link UI.Day} element. */\nexport enum DayFlag {\n  /** The day is disabled. */\n  disabled = \"disabled\",\n  /** The day is hidden. */\n  hidden = \"hidden\",\n  /** The day is outside the current month. */\n  outside = \"outside\",\n  /** The day is focused. */\n  focused = \"focused\",\n  /** The day is today. */\n  today = \"today\"\n}\n\n/**\n * Enum representing selection states that can be applied to the {@link UI.Day}\n * element in selection mode.\n */\nexport enum SelectionState {\n  /** The day is at the end of a selected range. */\n  range_end = \"range_end\",\n  /** The day is at the middle of a selected range. */\n  range_middle = \"range_middle\",\n  /** The day is at the start of a selected range. */\n  range_start = \"range_start\",\n  /** The day is selected. */\n  selected = \"selected\"\n}\n\n/**\n * Enum representing different animation states for transitioning between\n * months.\n */\nexport enum Animation {\n  /** The entering weeks when they appear before the exiting month. */\n  weeks_before_enter = \"weeks_before_enter\",\n  /** The exiting weeks when they disappear before the entering month. */\n  weeks_before_exit = \"weeks_before_exit\",\n  /** The entering weeks when they appear after the exiting month. */\n  weeks_after_enter = \"weeks_after_enter\",\n  /** The exiting weeks when they disappear after the entering month. */\n  weeks_after_exit = \"weeks_after_exit\",\n  /** The entering caption when it appears after the exiting month. */\n  caption_after_enter = \"caption_after_enter\",\n  /** The exiting caption when it disappears after the entering month. */\n  caption_after_exit = \"caption_after_exit\",\n  /** The entering caption when it appears before the exiting month. */\n  caption_before_enter = \"caption_before_enter\",\n  /** The exiting caption when it disappears before the entering month. */\n  caption_before_exit = \"caption_before_exit\"\n}\n\n/**\n * Deprecated UI elements and flags from previous versions of DayPicker.\n *\n * These elements are kept for backward compatibility and to assist in\n * transitioning to the new {@link UI} elements.\n *\n * @deprecated\n * @since 9.0.1\n * @template T - The type of the deprecated UI element (e.g., CSS class or\n *   style).\n * @see https://daypicker.dev/upgrading\n * @see https://daypicker.dev/docs/styling\n */\nexport type DeprecatedUI<T extends CSSProperties | string> = {\n  /**\n   * This element was applied to the style of any button in DayPicker and it is\n   * replaced by {@link UI.PreviousMonthButton} and {@link UI.NextMonthButton}.\n   *\n   * @deprecated\n   */\n  button: T;\n  /**\n   * This element was resetting the style of any button in DayPicker and it is\n   * replaced by {@link UI.PreviousMonthButton} and {@link UI.NextMonthButton}.\n   *\n   * @deprecated\n   */\n  button_reset: T;\n  /**\n   * This element has been renamed to {@link UI.MonthCaption}.\n   *\n   * @deprecated\n   */\n  caption: T;\n  /**\n   * This element has been removed. Captions are styled via\n   * {@link UI.MonthCaption}.\n   *\n   * @deprecated\n   */\n  caption_between: T;\n  /**\n   * This element has been renamed to {@link UI.Dropdowns}.\n   *\n   * @deprecated\n   */\n  caption_dropdowns: T;\n  /**\n   * This element has been removed. Captions are styled via\n   * {@link UI.MonthCaption}.\n   *\n   * @deprecated\n   */\n  caption_end: T;\n  /**\n   * This element has been removed.\n   *\n   * @deprecated\n   */\n  caption_start: T;\n  /**\n   * This element has been renamed to {@link UI.Day}.\n   *\n   * @deprecated\n   */\n  cell: T;\n  /**\n   * This element has been renamed to {@link DayFlag.disabled}.\n   *\n   * @deprecated\n   */\n  day_disabled: T;\n  /**\n   * This element has been renamed to {@link DayFlag.hidden}.\n   *\n   * @deprecated\n   */\n  day_hidden: T;\n  /**\n   * This element has been renamed to {@link DayFlag.outside}.\n   *\n   * @deprecated\n   */\n  day_outside: T;\n  /**\n   * This element has been renamed to {@link SelectionState.range_end}.\n   *\n   * @deprecated\n   */\n  day_range_end: T;\n  /**\n   * This element has been renamed to {@link SelectionState.range_middle}.\n   *\n   * @deprecated\n   */\n  day_range_middle: T;\n  /**\n   * This element has been renamed to {@link SelectionState.range_start}.\n   *\n   * @deprecated\n   */\n  day_range_start: T;\n  /**\n   * This element has been renamed to {@link SelectionState.selected}.\n   *\n   * @deprecated\n   */\n  day_selected: T;\n  /**\n   * This element has been renamed to {@link DayFlag.today}.\n   *\n   * @deprecated\n   */\n  day_today: T;\n  /**\n   * This element has been removed. The dropdown icon is now {@link UI.Chevron}\n   * inside a {@link UI.CaptionLabel}.\n   *\n   * @deprecated\n   */\n  dropdown_icon: T;\n  /**\n   * This element has been renamed to {@link UI.MonthsDropdown}.\n   *\n   * @deprecated\n   */\n  dropdown_month: T;\n  /**\n   * This element has been renamed to {@link UI.YearsDropdown}.\n   *\n   * @deprecated\n   */\n  dropdown_year: T;\n  /**\n   * This element has been removed.\n   *\n   * @deprecated\n   */\n  head: T;\n  /**\n   * This element has been renamed to {@link UI.Weekday}.\n   *\n   * @deprecated\n   */\n  head_cell: T;\n  /**\n   * This element has been renamed to {@link UI.Weekdays}.\n   *\n   * @deprecated\n   */\n  head_row: T;\n  /**\n   * This flag has been removed. Use `data-multiple-months` in your CSS\n   * selectors.\n   *\n   * @deprecated\n   */\n  multiple_months: T;\n  /**\n   * This element has been removed. To style the navigation buttons, use\n   * {@link UI.PreviousMonthButton} and {@link UI.NextMonthButton}.\n   *\n   * @deprecated\n   */\n  nav_button: T;\n  /**\n   * This element has been renamed to {@link UI.NextMonthButton}.\n   *\n   * @deprecated\n   */\n  nav_button_next: T;\n  /**\n   * This element has been renamed to {@link UI.PreviousMonthButton}.\n   *\n   * @deprecated\n   */\n  nav_button_previous: T;\n  /**\n   * This element has been removed. The dropdown icon is now {@link UI.Chevron}\n   * inside a {@link UI.NextMonthButton} or a {@link UI.PreviousMonthButton}.\n   *\n   * @deprecated\n   */\n  nav_icon: T;\n  /**\n   * This element has been renamed to {@link UI.Week}.\n   *\n   * @deprecated\n   */\n  row: T;\n  /**\n   * This element has been renamed to {@link UI.MonthGrid}.\n   *\n   * @deprecated\n   */\n  table: T;\n  /**\n   * This element has been renamed to {@link UI.Weeks}.\n   *\n   * @deprecated\n   */\n  tbody: T;\n  /**\n   * This element has been removed. The {@link UI.Footer} is now a single element\n   * below the months.\n   *\n   * @deprecated\n   */\n  tfoot: T;\n  /**\n   * This flag has been removed. There are no \"visually hidden\" elements in\n   * DayPicker 9.\n   *\n   * @deprecated\n   */\n  vhidden: T;\n  /**\n   * This element has been renamed. Use {@link UI.WeekNumber} instead.\n   *\n   * @deprecated\n   */\n  weeknumber: T;\n  /**\n   * This flag has been removed. Use `data-week-numbers` in your CSS.\n   *\n   * @deprecated\n   */\n  with_weeknumber: T;\n};\n", "import { DateLib } from \"../classes/index.js\";\n\nconst FIVE_WEEKS = 5;\nconst FOUR_WEEKS = 4;\n\n/**\n * Returns the number of weeks to display in the broadcast calendar for a given\n * month.\n *\n * The broadcast calendar may have either 4 or 5 weeks in a month, depending on\n * the start and end dates of the broadcast weeks.\n *\n * @since 9.4.0\n * @param month The month for which to calculate the number of weeks.\n * @param dateLib The date library to use for date manipulation.\n * @returns The number of weeks in the broadcast calendar (4 or 5).\n */\nexport function getBroadcastWeeksInMonth(month: Date, dateLib: DateLib): 4 | 5 {\n  // Get the first day of the month\n  const firstDayOfMonth = dateLib.startOfMonth(month);\n\n  // Get the day of the week for the first day of the month (1-7, where 1 is Monday)\n  const firstDayOfWeek =\n    firstDayOfMonth.getDay() > 0 ? firstDayOfMonth.getDay() : 7;\n\n  const broadcastStartDate = dateLib.addDays(month, -firstDayOfWeek + 1);\n\n  const lastDateOfLastWeek = dateLib.addDays(\n    broadcastStartDate,\n    FIVE_WEEKS * 7 - 1\n  );\n  const numberOfWeeks =\n    dateLib.getMonth(month) === dateLib.getMonth(lastDateOfLastWeek)\n      ? FIVE_WEEKS\n      : FOUR_WEEKS;\n\n  return numberOfWeeks;\n}\n", "import type { DateLib } from \"../classes/index.js\";\n\n/**\n * Returns the start date of the week in the broadcast calendar.\n *\n * The broadcast week starts on Monday. If the first day of the month is not a\n * Monday, this function calculates the previous Monday as the start of the\n * broadcast week.\n *\n * @since 9.4.0\n * @param date The date for which to calculate the start of the broadcast week.\n * @param dateLib The date library to use for date manipulation.\n * @returns The start date of the broadcast week.\n */\nexport function startOfBroadcastWeek(date: Date, dateLib: DateLib): Date {\n  const firstOfMonth = dateLib.startOfMonth(date);\n  const dayOfWeek = firstOfMonth.getDay();\n\n  if (dayOfWeek === 1) {\n    return firstOfMonth;\n  } else if (dayOfWeek === 0) {\n    return dateLib.addDays(firstOfMonth, -1 * 6);\n  } else {\n    return dateLib.addDays(firstOfMonth, -1 * (dayOfWeek - 1));\n  }\n}\n", "import type { DateLib } from \"../classes/index.js\";\n\nimport { getBroadcastWeeksInMonth } from \"./getBroadcastWeeksInMonth.js\";\nimport { startOfBroadcastWeek } from \"./startOfBroadcastWeek.js\";\n\n/**\n * Returns the end date of the week in the broadcast calendar.\n *\n * The broadcast week ends on the last day of the last broadcast week for the\n * given date.\n *\n * @since 9.4.0\n * @param date The date for which to calculate the end of the broadcast week.\n * @param dateLib The date library to use for date manipulation.\n * @returns The end date of the broadcast week.\n */\nexport function endOfBroadcastWeek(date: Date, dateLib: DateLib): Date {\n  const startDate = startOfBroadcastWeek(date, dateLib);\n  const numberOfWeeks = getBroadcastWeeksInMonth(date, dateLib);\n  const endDate = dateLib.addDays(startDate, numberOfWeeks * 7 - 1);\n  return endDate;\n}\n", "import { TZDate } from \"@date-fns/tz\";\nimport {\n  addDays,\n  addMonths,\n  addWeeks,\n  addYears,\n  differenceInCalendarDays,\n  differenceInCalendarMonths,\n  eachMonthOfInterval,\n  endOfISOWeek,\n  endOfMonth,\n  endOfWeek,\n  endOfYear,\n  format,\n  getISOWeek,\n  getMonth,\n  getWeek,\n  getYear,\n  isAfter,\n  isBefore,\n  isDate,\n  isSameDay,\n  isSameMonth,\n  isSameYear,\n  max,\n  min,\n  setMonth,\n  setYear,\n  startOfDay,\n  startOfISOWeek,\n  startOfMonth,\n  startOfWeek,\n  startOfYear\n} from \"date-fns\";\nimport type {\n  EndOfWeekOptions,\n  StartOfWeekOptions,\n  FormatOptions as DateFnsFormatOptions,\n  Interval,\n  GetMonthOptions,\n  GetYearOptions,\n  GetWeekOptions\n} from \"date-fns\";\nimport type { Locale } from \"date-fns/locale\";\nimport { enUS } from \"date-fns/locale/en-US\";\n\nimport { endOfBroadcastWeek } from \"../helpers/endOfBroadcastWeek.js\";\nimport { startOfBroadcastWeek } from \"../helpers/startOfBroadcastWeek.js\";\nimport { Numerals } from \"../types/shared.js\";\n\nexport type { Locale } from \"date-fns/locale\";\nexport type { Month as DateFnsMonth } from \"date-fns\";\n\n/**\n * @ignore\n * @deprecated Use {@link DateLibOptions} instead.\n */\nexport type FormatOptions = DateLibOptions;\n/**\n * @ignore\n * @deprecated Use {@link DateLibOptions} instead.\n */\nexport type LabelOptions = DateLibOptions;\n\n/**\n * The options for the `DateLib` class.\n *\n * Extends `date-fns` [format](https://date-fns.org/docs/format),\n * [startOfWeek](https://date-fns.org/docs/startOfWeek) and\n * [endOfWeek](https://date-fns.org/docs/endOfWeek) options.\n *\n * @since 9.2.0\n */\nexport interface DateLibOptions\n  extends DateFnsFormatOptions,\n    StartOfWeekOptions,\n    EndOfWeekOptions {\n  /** A constructor for the `Date` object. */\n  Date?: typeof Date;\n  /** A locale to use for formatting dates. */\n  locale?: Locale;\n  /**\n   * A time zone to use for dates.\n   *\n   * @since 9.5.0\n   */\n  timeZone?: string;\n  /**\n   * The numbering system to use for formatting numbers.\n   *\n   * @since 9.5.0\n   */\n  numerals?: Numerals;\n}\n\n/**\n * A wrapper class around [date-fns](http://date-fns.org) that provides utility\n * methods for date manipulation and formatting.\n *\n * @since 9.2.0\n * @example\n *   const dateLib = new DateLib({ locale: es });\n *   const newDate = dateLib.addDays(new Date(), 5);\n */\nexport class DateLib {\n  /** The options for configuring the date library. */\n  readonly options: DateLibOptions;\n\n  /** Overrides for the default date library functions. */\n  readonly overrides?: Partial<typeof DateLib.prototype>;\n\n  /**\n   * Creates an instance of `DateLib`.\n   *\n   * @param options Configuration options for the date library.\n   * @param overrides Custom overrides for the date library functions.\n   */\n  constructor(\n    options?: DateLibOptions,\n    overrides?: Partial<typeof DateLib.prototype>\n  ) {\n    this.options = { locale: enUS, ...options };\n    this.overrides = overrides;\n  }\n\n  /**\n   * Generates a mapping of Arabic digits (0-9) to the target numbering system\n   * digits.\n   *\n   * @since 9.5.0\n   * @returns A record mapping Arabic digits to the target numerals.\n   */\n  private getDigitMap(): Record<string, string> {\n    const { numerals = \"latn\" } = this.options;\n\n    // Use Intl.NumberFormat to create a formatter with the specified numbering system\n    const formatter = new Intl.NumberFormat(\"en-US\", {\n      numberingSystem: numerals\n    });\n\n    // Map Arabic digits (0-9) to the target numerals\n    const digitMap: Record<string, string> = {};\n    for (let i = 0; i < 10; i++) {\n      digitMap[i.toString()] = formatter.format(i);\n    }\n\n    return digitMap;\n  }\n\n  /**\n   * Replaces Arabic digits in a string with the target numbering system digits.\n   *\n   * @since 9.5.0\n   * @param input The string containing Arabic digits.\n   * @returns The string with digits replaced.\n   */\n  private replaceDigits(input: string): string {\n    const digitMap = this.getDigitMap();\n    return input.replace(/\\d/g, (digit) => digitMap[digit] || digit);\n  }\n\n  /**\n   * Formats a number using the configured numbering system.\n   *\n   * @since 9.5.0\n   * @param value The number to format.\n   * @returns The formatted number as a string.\n   */\n  formatNumber(value: number | string): string {\n    return this.replaceDigits(value.toString());\n  }\n\n  /**\n   * Reference to the built-in Date constructor.\n   *\n   * @deprecated Use `newDate()` or `today()`.\n   */\n  Date: typeof Date = Date;\n\n  /**\n   * Creates a new `Date` object representing today's date.\n   *\n   * @since 9.5.0\n   * @returns A `Date` object for today's date.\n   */\n  today = (): Date => {\n    if (this.overrides?.today) {\n      return this.overrides.today();\n    }\n    if (this.options.timeZone) {\n      return TZDate.tz(this.options.timeZone);\n    }\n    return new this.Date();\n  };\n\n  /**\n   * Creates a new `Date` object with the specified year, month, and day.\n   *\n   * @since 9.5.0\n   * @param year The year.\n   * @param monthIndex The month (0-11).\n   * @param date The day of the month.\n   * @returns A new `Date` object.\n   */\n  newDate = (year: number, monthIndex: number, date: number): Date => {\n    if (this.overrides?.newDate) {\n      return this.overrides.newDate(year, monthIndex, date);\n    }\n    if (this.options.timeZone) {\n      return new TZDate(year, monthIndex, date, this.options.timeZone);\n    }\n    return new Date(year, monthIndex, date);\n  };\n\n  /**\n   * Adds the specified number of days to the given date.\n   *\n   * @param date The date to add days to.\n   * @param amount The number of days to add.\n   * @returns The new date with the days added.\n   */\n  addDays = (date: Date, amount: number): Date => {\n    return this.overrides?.addDays\n      ? this.overrides.addDays(date, amount)\n      : addDays(date, amount);\n  };\n\n  /**\n   * Adds the specified number of months to the given date.\n   *\n   * @param date The date to add months to.\n   * @param amount The number of months to add.\n   * @returns The new date with the months added.\n   */\n  addMonths = (date: Date, amount: number): Date => {\n    return this.overrides?.addMonths\n      ? this.overrides.addMonths(date, amount)\n      : addMonths(date, amount);\n  };\n\n  /**\n   * Adds the specified number of weeks to the given date.\n   *\n   * @param date The date to add weeks to.\n   * @param amount The number of weeks to add.\n   * @returns The new date with the weeks added.\n   */\n  addWeeks = (date: Date, amount: number): Date => {\n    return this.overrides?.addWeeks\n      ? this.overrides.addWeeks(date, amount)\n      : addWeeks(date, amount);\n  };\n\n  /**\n   * Adds the specified number of years to the given date.\n   *\n   * @param date The date to add years to.\n   * @param amount The number of years to add.\n   * @returns The new date with the years added.\n   */\n  addYears = (date: Date, amount: number): Date => {\n    return this.overrides?.addYears\n      ? this.overrides.addYears(date, amount)\n      : addYears(date, amount);\n  };\n\n  /**\n   * Returns the number of calendar days between the given dates.\n   *\n   * @param dateLeft The later date.\n   * @param dateRight The earlier date.\n   * @returns The number of calendar days between the dates.\n   */\n  differenceInCalendarDays = (dateLeft: Date, dateRight: Date): number => {\n    return this.overrides?.differenceInCalendarDays\n      ? this.overrides.differenceInCalendarDays(dateLeft, dateRight)\n      : differenceInCalendarDays(dateLeft, dateRight);\n  };\n\n  /**\n   * Returns the number of calendar months between the given dates.\n   *\n   * @param dateLeft The later date.\n   * @param dateRight The earlier date.\n   * @returns The number of calendar months between the dates.\n   */\n  differenceInCalendarMonths = (dateLeft: Date, dateRight: Date): number => {\n    return this.overrides?.differenceInCalendarMonths\n      ? this.overrides.differenceInCalendarMonths(dateLeft, dateRight)\n      : differenceInCalendarMonths(dateLeft, dateRight);\n  };\n\n  /**\n   * Returns the months between the given dates.\n   *\n   * @param interval The interval to get the months for.\n   */\n  eachMonthOfInterval = (interval: Interval): Date[] => {\n    return this.overrides?.eachMonthOfInterval\n      ? this.overrides.eachMonthOfInterval(interval)\n      : eachMonthOfInterval(interval);\n  };\n\n  /**\n   * Returns the end of the broadcast week for the given date.\n   *\n   * @param date The original date.\n   * @returns The end of the broadcast week.\n   */\n  endOfBroadcastWeek = (date: Date): Date => {\n    return this.overrides?.endOfBroadcastWeek\n      ? this.overrides.endOfBroadcastWeek(date)\n      : endOfBroadcastWeek(date, this);\n  };\n\n  /**\n   * Returns the end of the ISO week for the given date.\n   *\n   * @param date The original date.\n   * @returns The end of the ISO week.\n   */\n  endOfISOWeek = (date: Date): Date => {\n    return this.overrides?.endOfISOWeek\n      ? this.overrides.endOfISOWeek(date)\n      : endOfISOWeek(date);\n  };\n\n  /**\n   * Returns the end of the month for the given date.\n   *\n   * @param date The original date.\n   * @returns The end of the month.\n   */\n  endOfMonth = (date: Date): Date => {\n    return this.overrides?.endOfMonth\n      ? this.overrides.endOfMonth(date)\n      : endOfMonth(date);\n  };\n\n  /**\n   * Returns the end of the week for the given date.\n   *\n   * @param date The original date.\n   * @returns The end of the week.\n   */\n  endOfWeek = (date: Date, options?: EndOfWeekOptions<Date>): Date => {\n    return this.overrides?.endOfWeek\n      ? this.overrides.endOfWeek(date, options)\n      : endOfWeek(date, this.options);\n  };\n\n  /**\n   * Returns the end of the year for the given date.\n   *\n   * @param date The original date.\n   * @returns The end of the year.\n   */\n  endOfYear = (date: Date): Date => {\n    return this.overrides?.endOfYear\n      ? this.overrides.endOfYear(date)\n      : endOfYear(date);\n  };\n\n  /**\n   * Formats the given date using the specified format string.\n   *\n   * @param date The date to format.\n   * @param formatStr The format string.\n   * @returns The formatted date string.\n   */\n  format = (\n    date: Date,\n    formatStr: string,\n    options?: DateFnsFormatOptions\n  ): string => {\n    const formatted = this.overrides?.format\n      ? this.overrides.format(date, formatStr, this.options)\n      : format(date, formatStr, this.options);\n    if (this.options.numerals && this.options.numerals !== \"latn\") {\n      return this.replaceDigits(formatted);\n    }\n    return formatted;\n  };\n\n  /**\n   * Returns the ISO week number for the given date.\n   *\n   * @param date The date to get the ISO week number for.\n   * @returns The ISO week number.\n   */\n  getISOWeek = (date: Date): number => {\n    return this.overrides?.getISOWeek\n      ? this.overrides.getISOWeek(date)\n      : getISOWeek(date);\n  };\n\n  /**\n   * Returns the month of the given date.\n   *\n   * @param date The date to get the month for.\n   * @returns The month.\n   */\n  getMonth = (date: Date, options?: GetMonthOptions): number => {\n    return this.overrides?.getMonth\n      ? this.overrides.getMonth(date, this.options)\n      : getMonth(date, this.options);\n  };\n\n  /**\n   * Returns the year of the given date.\n   *\n   * @param date The date to get the year for.\n   * @returns The year.\n   */\n  getYear = (date: Date, options?: GetYearOptions): number => {\n    return this.overrides?.getYear\n      ? this.overrides.getYear(date, this.options)\n      : getYear(date, this.options);\n  };\n\n  /**\n   * Returns the local week number for the given date.\n   *\n   * @param date The date to get the week number for.\n   * @returns The week number.\n   */\n  getWeek = (date: Date, options?: GetWeekOptions): number => {\n    return this.overrides?.getWeek\n      ? this.overrides.getWeek(date, this.options)\n      : getWeek(date, this.options);\n  };\n\n  /**\n   * Checks if the first date is after the second date.\n   *\n   * @param date The date to compare.\n   * @param dateToCompare The date to compare with.\n   * @returns True if the first date is after the second date.\n   */\n  isAfter = (date: Date, dateToCompare: Date): boolean => {\n    return this.overrides?.isAfter\n      ? this.overrides.isAfter(date, dateToCompare)\n      : isAfter(date, dateToCompare);\n  };\n\n  /**\n   * Checks if the first date is before the second date.\n   *\n   * @param date The date to compare.\n   * @param dateToCompare The date to compare with.\n   * @returns True if the first date is before the second date.\n   */\n  isBefore = (date: Date, dateToCompare: Date): boolean => {\n    return this.overrides?.isBefore\n      ? this.overrides.isBefore(date, dateToCompare)\n      : isBefore(date, dateToCompare);\n  };\n\n  /**\n   * Checks if the given value is a Date object.\n   *\n   * @param value The value to check.\n   * @returns True if the value is a Date object.\n   */\n  isDate: (value: unknown) => value is Date = (value): value is Date => {\n    return this.overrides?.isDate\n      ? this.overrides.isDate(value)\n      : isDate(value);\n  };\n\n  /**\n   * Checks if the given dates are on the same day.\n   *\n   * @param dateLeft The first date to compare.\n   * @param dateRight The second date to compare.\n   * @returns True if the dates are on the same day.\n   */\n  isSameDay = (dateLeft: Date, dateRight: Date): boolean => {\n    return this.overrides?.isSameDay\n      ? this.overrides.isSameDay(dateLeft, dateRight)\n      : isSameDay(dateLeft, dateRight);\n  };\n\n  /**\n   * Checks if the given dates are in the same month.\n   *\n   * @param dateLeft The first date to compare.\n   * @param dateRight The second date to compare.\n   * @returns True if the dates are in the same month.\n   */\n  isSameMonth = (dateLeft: Date, dateRight: Date): boolean => {\n    return this.overrides?.isSameMonth\n      ? this.overrides.isSameMonth(dateLeft, dateRight)\n      : isSameMonth(dateLeft, dateRight);\n  };\n\n  /**\n   * Checks if the given dates are in the same year.\n   *\n   * @param dateLeft The first date to compare.\n   * @param dateRight The second date to compare.\n   * @returns True if the dates are in the same year.\n   */\n  isSameYear = (dateLeft: Date, dateRight: Date): boolean => {\n    return this.overrides?.isSameYear\n      ? this.overrides.isSameYear(dateLeft, dateRight)\n      : isSameYear(dateLeft, dateRight);\n  };\n\n  /**\n   * Returns the latest date in the given array of dates.\n   *\n   * @param dates The array of dates to compare.\n   * @returns The latest date.\n   */\n  max = (dates: Date[]): Date => {\n    return this.overrides?.max ? this.overrides.max(dates) : max(dates);\n  };\n\n  /**\n   * Returns the earliest date in the given array of dates.\n   *\n   * @param dates The array of dates to compare.\n   * @returns The earliest date.\n   */\n  min = (dates: Date[]): Date => {\n    return this.overrides?.min ? this.overrides.min(dates) : min(dates);\n  };\n\n  /**\n   * Sets the month of the given date.\n   *\n   * @param date The date to set the month on.\n   * @param month The month to set (0-11).\n   * @returns The new date with the month set.\n   */\n  setMonth = (date: Date, month: number): Date => {\n    return this.overrides?.setMonth\n      ? this.overrides.setMonth(date, month)\n      : setMonth(date, month);\n  };\n\n  /**\n   * Sets the year of the given date.\n   *\n   * @param date The date to set the year on.\n   * @param year The year to set.\n   * @returns The new date with the year set.\n   */\n  setYear = (date: Date, year: number): Date => {\n    return this.overrides?.setYear\n      ? this.overrides.setYear(date, year)\n      : setYear(date, year);\n  };\n\n  /**\n   * Returns the start of the broadcast week for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the broadcast week.\n   */\n  startOfBroadcastWeek = (date: Date, dateLib: DateLib): Date => {\n    return this.overrides?.startOfBroadcastWeek\n      ? this.overrides.startOfBroadcastWeek(date, this)\n      : startOfBroadcastWeek(date, this);\n  };\n\n  /**\n   * Returns the start of the day for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the day.\n   */\n  startOfDay = (date: Date): Date => {\n    return this.overrides?.startOfDay\n      ? this.overrides.startOfDay(date)\n      : startOfDay(date);\n  };\n\n  /**\n   * Returns the start of the ISO week for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the ISO week.\n   */\n  startOfISOWeek = (date: Date): Date => {\n    return this.overrides?.startOfISOWeek\n      ? this.overrides.startOfISOWeek(date)\n      : startOfISOWeek(date);\n  };\n\n  /**\n   * Returns the start of the month for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the month.\n   */\n  startOfMonth = (date: Date): Date => {\n    return this.overrides?.startOfMonth\n      ? this.overrides.startOfMonth(date)\n      : startOfMonth(date);\n  };\n\n  /**\n   * Returns the start of the week for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the week.\n   */\n  startOfWeek = (date: Date, options?: StartOfWeekOptions): Date => {\n    return this.overrides?.startOfWeek\n      ? this.overrides.startOfWeek(date, this.options)\n      : startOfWeek(date, this.options);\n  };\n\n  /**\n   * Returns the start of the year for the given date.\n   *\n   * @param date The original date.\n   * @returns The start of the year.\n   */\n  startOfYear = (date: Date): Date => {\n    return this.overrides?.startOfYear\n      ? this.overrides.startOfYear(date)\n      : startOfYear(date);\n  };\n}\n/** The default locale (English). */\nexport { enUS as defaultLocale } from \"date-fns/locale/en-US\";\n\n/**\n * The default date library with English locale.\n *\n * @since 9.2.0\n */\nexport const defaultDateLib = new DateLib();\n\n/**\n * @ignore\n * @deprecated Use `defaultDateLib`.\n */\nexport const dateLib = defaultDateLib;\n", "import { type DateLib, defaultDateLib } from \"./DateLib.js\";\n\n/**\n * Represents a day displayed in the calendar.\n *\n * In DayPicker, a `CalendarDay` is a wrapper around a `Date` object that\n * provides additional information about the day, such as whether it belongs to\n * the displayed month.\n */\nexport class CalendarDay {\n  constructor(\n    date: Date,\n    displayMonth: Date,\n    dateLib: DateLib = defaultDateLib\n  ) {\n    this.date = date;\n    this.displayMonth = displayMonth;\n    this.outside = Boolean(\n      displayMonth && !dateLib.isSameMonth(date, displayMonth)\n    );\n    this.dateLib = dateLib;\n  }\n\n  /**\n   * Utility functions for manipulating dates.\n   *\n   * @private\n   */\n  readonly dateLib: DateLib;\n\n  /**\n   * Indicates whether the day does not belong to the displayed month.\n   *\n   * If `outside` is `true`, use `displayMonth` to determine the month to which\n   * the day belongs.\n   */\n  readonly outside: boolean;\n\n  /**\n   * The month that is currently displayed in the calendar.\n   *\n   * This property is useful for determining if the day belongs to the same\n   * month as the displayed month, especially when `showOutsideDays` is\n   * enabled.\n   */\n  readonly displayMonth: Date;\n\n  /** The date represented by this day. */\n  readonly date: Date;\n\n  /**\n   * Checks if this day is equal to another `CalendarDay`, considering both the\n   * date and the displayed month.\n   *\n   * @param day The `CalendarDay` to compare with.\n   * @returns `true` if the days are equal, otherwise `false`.\n   */\n  isEqualTo(day: CalendarDay) {\n    return (\n      this.dateLib.isSameDay(day.date, this.date) &&\n      this.dateLib.isSameMonth(day.displayMonth, this.displayMonth)\n    );\n  }\n}\n", "import { CalendarWeek } from \"./CalendarWeek.js\";\n\n/**\n * Represents a month in a calendar year.\n *\n * A `CalendarMonth` contains the weeks within the month and the date of the\n * month.\n */\nexport class CalendarMonth {\n  constructor(month: Date, weeks: CalendarWeek[]) {\n    this.date = month;\n    this.weeks = weeks;\n  }\n\n  /** The date representing the first day of the month. */\n  date: Date;\n\n  /** The weeks that belong to this month. */\n  weeks: CalendarWeek[];\n}\n", "import { CalendarDay } from \"./CalendarDay.js\";\n\n/**\n * Represents a week in a calendar month.\n *\n * A `CalendarWeek` contains the days within the week and the week number.\n */\nexport class CalendarWeek {\n  constructor(weekNumber: number, days: CalendarDay[]) {\n    this.days = days;\n    this.weekNumber = weekNumber;\n  }\n\n  /** The number of the week within the year. */\n  weekNumber: number;\n\n  /** The days that belong to this week. */\n  days: CalendarDay[];\n}\n", "import { defaultDateLib } from \"../classes/index.js\";\nimport type { DateRange } from \"../types/index.js\";\n\n/**\n * Checks if a given date is within a specified date range.\n *\n * @since 9.0.0\n * @param range - The date range to check against.\n * @param date - The date to check.\n * @param excludeEnds - If `true`, the range's start and end dates are excluded.\n * @param dateLib - The date utility library instance.\n * @returns `true` if the date is within the range, otherwise `false`.\n * @group Utilities\n */\nexport function rangeIncludesDate(\n  range: DateRange,\n  date: Date,\n  excludeEnds = false,\n  dateLib = defaultDateLib\n): boolean {\n  let { from, to } = range;\n  const { differenceInCalendarDays, isSameDay } = dateLib;\n  if (from && to) {\n    const isRangeInverted = differenceInCalendarDays(to, from) < 0;\n    if (isRangeInverted) {\n      [from, to] = [to, from];\n    }\n    const isInRange =\n      differenceInCalendarDays(date, from) >= (excludeEnds ? 1 : 0) &&\n      differenceInCalendarDays(to, date) >= (excludeEnds ? 1 : 0);\n    return isInRange;\n  }\n  if (!excludeEnds && to) {\n    return isSameDay(to, date);\n  }\n  if (!excludeEnds && from) {\n    return isSameDay(from, date);\n  }\n  return false;\n}\n\n/**\n * @private\n * @deprecated Use {@link rangeIncludesDate} instead.\n */\nexport const isDateInRange = (range: DateRange, date: Date) =>\n  rangeIncludesDate(range, date, false, defaultDateLib);\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport type {\n  DateAfter,\n  DateBefore,\n  DateInterval,\n  DateRange,\n  DayOfWeek\n} from \"../types/index.js\";\n\n/**\n * Checks if the given value is of type {@link DateInterval}.\n *\n * @param matcher - The value to check.\n * @returns `true` if the value is a {@link DateInterval}, otherwise `false`.\n * @group Utilities\n */\nexport function isDateInterval(matcher: unknown): matcher is DateInterval {\n  return Boolean(\n    matcher &&\n      typeof matcher === \"object\" &&\n      \"before\" in matcher &&\n      \"after\" in matcher\n  );\n}\n\n/**\n * Checks if the given value is of type {@link DateRange}.\n *\n * @param value - The value to check.\n * @returns `true` if the value is a {@link DateRange}, otherwise `false`.\n * @group Utilities\n */\nexport function isDateRange(value: unknown): value is DateRange {\n  return Boolean(value && typeof value === \"object\" && \"from\" in value);\n}\n\n/**\n * Checks if the given value is of type {@link DateAfter}.\n *\n * @param value - The value to check.\n * @returns `true` if the value is a {@link DateAfter}, otherwise `false`.\n * @group Utilities\n */\nexport function isDateAfterType(value: unknown): value is DateAfter {\n  return Boolean(value && typeof value === \"object\" && \"after\" in value);\n}\n\n/**\n * Checks if the given value is of type {@link DateBefore}.\n *\n * @param value - The value to check.\n * @returns `true` if the value is a {@link DateBefore}, otherwise `false`.\n * @group Utilities\n */\nexport function isDateBeforeType(value: unknown): value is DateBefore {\n  return Boolean(value && typeof value === \"object\" && \"before\" in value);\n}\n\n/**\n * Checks if the given value is of type {@link DayOfWeek}.\n *\n * @param value - The value to check.\n * @returns `true` if the value is a {@link DayOfWeek}, otherwise `false`.\n * @group Utilities\n */\nexport function isDayOfWeekType(value: unknown): value is DayOfWeek {\n  return Boolean(value && typeof value === \"object\" && \"dayOfWeek\" in value);\n}\n\n/**\n * Checks if the given value is an array of valid dates.\n *\n * @private\n * @param value - The value to check.\n * @param dateLib - The date utility library instance.\n * @returns `true` if the value is an array of valid dates, otherwise `false`.\n */\nexport function isDatesArray(\n  value: unknown,\n  dateLib: DateLib\n): value is Date[] {\n  return Array.isArray(value) && value.every(dateLib.isDate);\n}\n", "import { type DateLib, defaultDateLib } from \"../classes/DateLib.js\";\nimport type { Matcher } from \"../types/index.js\";\n\nimport { rangeIncludesDate } from \"./rangeIncludesDate.js\";\nimport {\n  isDateAfterType,\n  isDateBeforeType,\n  isDateInterval,\n  isDateRange,\n  isDatesArray,\n  isDayOfWeekType\n} from \"./typeguards.js\";\n\n/**\n * Checks if a given date matches at least one of the specified {@link Matcher}.\n *\n * @param date - The date to check.\n * @param matchers - The matchers to check against.\n * @param dateLib - The date utility library instance.\n * @returns `true` if the date matches any of the matchers, otherwise `false`.\n * @group Utilities\n */\nexport function dateMatchModifiers(\n  date: Date,\n  matchers: Matcher | Matcher[],\n  dateLib: DateLib = defaultDateLib\n): boolean {\n  const matchersArr = !Array.isArray(matchers) ? [matchers] : matchers;\n  const { isSameDay, differenceInCalendarDays, isAfter } = dateLib;\n  return matchersArr.some((matcher: Matcher) => {\n    if (typeof matcher === \"boolean\") {\n      return matcher;\n    }\n    if (dateLib.isDate(matcher)) {\n      return isSameDay(date, matcher);\n    }\n    if (isDatesArray(matcher, dateLib)) {\n      return matcher.includes(date);\n    }\n    if (isDateRange(matcher)) {\n      return rangeIncludesDate(matcher, date, false, dateLib);\n    }\n    if (isDayOfWeekType(matcher)) {\n      if (!Array.isArray(matcher.dayOfWeek)) {\n        return matcher.dayOfWeek === date.getDay();\n      }\n      return matcher.dayOfWeek.includes(date.getDay());\n    }\n    if (isDateInterval(matcher)) {\n      const diffBefore = differenceInCalendarDays(matcher.before, date);\n      const diffAfter = differenceInCalendarDays(matcher.after, date);\n      const isDayBefore = diffBefore > 0;\n      const isDayAfter = diffAfter < 0;\n      const isClosedInterval = isAfter(matcher.before, matcher.after);\n      if (isClosedInterval) {\n        return isDayAfter && isDayBefore;\n      } else {\n        return isDayBefore || isDayAfter;\n      }\n    }\n    if (isDateAfterType(matcher)) {\n      return differenceInCalendarDays(date, matcher.after) > 0;\n    }\n    if (isDateBeforeType(matcher)) {\n      return differenceInCalendarDays(matcher.before, date) > 0;\n    }\n    if (typeof matcher === \"function\") {\n      return matcher(date);\n    }\n    return false;\n  });\n}\n\n/**\n * @private\n * @deprecated Use {@link dateMatchModifiers} instead.\n */\nexport const isMatch = dateMatchModifiers;\n", "import { DayFlag } from \"../UI.js\";\nimport type { CalendarDay, DateLib } from \"../classes/index.js\";\nimport type { DayPickerProps, Modifiers } from \"../types/index.js\";\nimport { dateMatchModifiers } from \"../utils/dateMatchModifiers.js\";\n\n/**\n * Creates a function to retrieve the modifiers for a given day.\n *\n * This function calculates both internal and custom modifiers for each day\n * based on the provided calendar days and DayPicker props.\n *\n * @private\n * @param days The array of `CalendarDay` objects to process.\n * @param props The DayPicker props, including modifiers and configuration\n *   options.\n * @param dateLib The date library to use for date manipulation.\n * @returns A function that retrieves the modifiers for a given `CalendarDay`.\n */\nexport function createGetModifiers(\n  days: CalendarDay[],\n  props: DayPickerProps,\n  navStart: Date | undefined,\n  navEnd: Date | undefined,\n  dateLib: DateLib\n) {\n  const {\n    disabled,\n    hidden,\n    modifiers,\n    showOutsideDays,\n    broadcastCalendar,\n    today\n  } = props;\n\n  const {\n    isSameDay,\n    isSameMonth,\n    startOfMonth,\n    isBefore,\n    endOfMonth,\n    isAfter\n  } = dateLib;\n\n  const computedNavStart = navStart && startOfMonth(navStart);\n  const computedNavEnd = navEnd && endOfMonth(navEnd);\n\n  const internalModifiersMap: Record<DayFlag, CalendarDay[]> = {\n    [DayFlag.focused]: [],\n    [DayFlag.outside]: [],\n    [DayFlag.disabled]: [],\n    [DayFlag.hidden]: [],\n    [DayFlag.today]: []\n  };\n\n  const customModifiersMap: Record<string, CalendarDay[]> = {};\n\n  for (const day of days) {\n    const { date, displayMonth } = day;\n\n    const isOutside = Boolean(displayMonth && !isSameMonth(date, displayMonth));\n\n    const isBeforeNavStart = Boolean(\n      computedNavStart && isBefore(date, computedNavStart)\n    );\n\n    const isAfterNavEnd = Boolean(\n      computedNavEnd && isAfter(date, computedNavEnd)\n    );\n\n    const isDisabled = Boolean(\n      disabled && dateMatchModifiers(date, disabled, dateLib)\n    );\n\n    const isHidden =\n      Boolean(hidden && dateMatchModifiers(date, hidden, dateLib)) ||\n      isBeforeNavStart ||\n      isAfterNavEnd ||\n      // Broadcast calendar will show outside days as default\n      (!broadcastCalendar && !showOutsideDays && isOutside) ||\n      (broadcastCalendar && showOutsideDays === false && isOutside);\n\n    const isToday = isSameDay(date, today ?? dateLib.today());\n\n    if (isOutside) internalModifiersMap.outside.push(day);\n    if (isDisabled) internalModifiersMap.disabled.push(day);\n    if (isHidden) internalModifiersMap.hidden.push(day);\n    if (isToday) internalModifiersMap.today.push(day);\n\n    // Add custom modifiers\n    if (modifiers) {\n      Object.keys(modifiers).forEach((name) => {\n        const modifierValue = modifiers?.[name];\n        const isMatch = modifierValue\n          ? dateMatchModifiers(date, modifierValue, dateLib)\n          : false;\n        if (!isMatch) return;\n        if (customModifiersMap[name]) {\n          customModifiersMap[name].push(day);\n        } else {\n          customModifiersMap[name] = [day];\n        }\n      });\n    }\n  }\n\n  return (day: CalendarDay): Modifiers => {\n    // Initialize all the modifiers to false\n    const dayFlags: Record<DayFlag, boolean> = {\n      [DayFlag.focused]: false,\n      [DayFlag.disabled]: false,\n      [DayFlag.hidden]: false,\n      [DayFlag.outside]: false,\n      [DayFlag.today]: false\n    };\n    const customModifiers: Modifiers = {};\n\n    // Find the modifiers for the given day\n    for (const name in internalModifiersMap) {\n      const days = internalModifiersMap[name as DayFlag];\n      dayFlags[name as DayFlag] = days.some((d) => d === day);\n    }\n    for (const name in customModifiersMap) {\n      customModifiers[name] = customModifiersMap[name].some((d) => d === day);\n    }\n\n    return {\n      ...dayFlags,\n      // custom modifiers should override all the previous ones\n      ...customModifiers\n    };\n  };\n}\n", "import { DayFlag, SelectionState, UI } from \"../UI.js\";\nimport type { ModifiersClassNames, ClassNames } from \"../types/index.js\";\n\n/**\n * Returns the class names for a day based on its modifiers.\n *\n * This function combines the base class name for the day with any class names\n * associated with active modifiers.\n *\n * @param modifiers The modifiers applied to the day.\n * @param classNames The base class names for the calendar elements.\n * @param modifiersClassNames The class names associated with specific\n *   modifiers.\n * @returns An array of class names for the day.\n */\nexport function getClassNamesForModifiers(\n  modifiers: Record<string, boolean>,\n  classNames: ClassNames,\n  modifiersClassNames: ModifiersClassNames = {}\n): string[] {\n  const modifierClassNames = Object.entries(modifiers)\n    .filter(([, active]) => active === true)\n    .reduce(\n      (previousValue, [key]) => {\n        if (modifiersClassNames[key]) {\n          previousValue.push(modifiersClassNames[key as string]);\n        } else if (classNames[DayFlag[key as DayFlag]]) {\n          previousValue.push(classNames[DayFlag[key as DayFlag]]);\n        } else if (classNames[SelectionState[key as SelectionState]]) {\n          previousValue.push(classNames[SelectionState[key as SelectionState]]);\n        }\n        return previousValue;\n      },\n      [classNames[UI.Day]] as string[]\n    );\n\n  return modifierClassNames;\n}\n", "export * from \"./Button.js\";\nexport * from \"./CaptionLabel.js\";\nexport * from \"./Chevron.js\";\nexport * from \"./Day.js\";\nexport * from \"./DayButton.js\";\nexport * from \"./Dropdown.js\";\nexport * from \"./DropdownNav.js\";\nexport * from \"./Footer.js\";\nexport * from \"./Month.js\";\nexport * from \"./MonthCaption.js\";\nexport * from \"./MonthGrid.js\";\nexport * from \"./Months.js\";\nexport * from \"./MonthsDropdown.js\";\nexport * from \"./Nav.js\";\nexport * from \"./NextMonthButton.js\";\nexport * from \"./Option.js\";\nexport * from \"./PreviousMonthButton.js\";\nexport * from \"./Root.js\";\nexport * from \"./Select.js\";\nexport * from \"./Week.js\";\nexport * from \"./Weekday.js\";\nexport * from \"./Weekdays.js\";\nexport * from \"./WeekNumber.js\";\nexport * from \"./WeekNumberHeader.js\";\nexport * from \"./Weeks.js\";\nexport * from \"./YearsDropdown.js\";\n", "import React, { type ButtonHTMLAttributes } from \"react\";\n\n/**\n * Render the button elements in the calendar.\n *\n * @private\n * @deprecated Use `PreviousMonthButton` or `@link NextMonthButton` instead.\n */\nexport function Button(props: ButtonHTMLAttributes<HTMLButtonElement>) {\n  return <button {...props} />;\n}\n\nexport type ButtonProps = Parameters<typeof Button>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render the label in the month caption.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function CaptionLabel(props: HTMLAttributes<HTMLSpanElement>) {\n  return <span {...props} />;\n}\n\nexport type CaptionLabelProps = Parameters<typeof CaptionLabel>[0];\n", "import React from \"react\";\n\n/**\n * Render the chevron icon used in the navigation buttons and dropdowns.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Chevron(props: {\n  className?: string;\n  /**\n   * The size of the chevron.\n   *\n   * @defaultValue 24\n   */\n  size?: number;\n  /** Set to `true` to disable the chevron. */\n  disabled?: boolean;\n  /** The orientation of the chevron. */\n  orientation?: \"up\" | \"down\" | \"left\" | \"right\";\n}) {\n  const { size = 24, orientation = \"left\", className } = props;\n\n  return (\n    <svg className={className} width={size} height={size} viewBox=\"0 0 24 24\">\n      {orientation === \"up\" && (\n        <polygon points=\"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28\" />\n      )}\n      {orientation === \"down\" && (\n        <polygon points=\"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72\" />\n      )}\n      {orientation === \"left\" && (\n        <polygon points=\"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20\" />\n      )}\n      {orientation === \"right\" && (\n        <polygon points=\"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20\" />\n      )}\n    </svg>\n  );\n}\n\nexport type ChevronProps = Parameters<typeof Chevron>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\nimport type { CalendarDay } from \"../classes/index.js\";\nimport type { Modifiers } from \"../types/index.js\";\n\n/**\n * Render a grid cell for a specific day in the calendar.\n *\n * Handles interaction and focus for the day. If you only need to change the\n * content of the day cell, consider swapping the `DayButton` component\n * instead.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Day(\n  props: {\n    /** The day to render. */\n    day: CalendarDay;\n    /** The modifiers to apply to the day. */\n    modifiers: Modifiers;\n  } & HTMLAttributes<HTMLDivElement>\n) {\n  const { day, modifiers, ...tdProps } = props;\n  return <td {...tdProps} />;\n}\n\nexport type DayProps = Parameters<typeof Day>[0];\n", "import React, { type ButtonHTMLAttributes } from \"react\";\n\nimport type { CalendarDay } from \"../classes/index.js\";\nimport type { Modifiers } from \"../types/index.js\";\n\n/**\n * Render a button for a specific day in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function DayButton(\n  props: {\n    /** The day to render. */\n    day: CalendarDay;\n    /** The modifiers to apply to the day. */\n    modifiers: Modifiers;\n  } & ButtonHTMLAttributes<HTMLButtonElement>\n) {\n  const { day, modifiers, ...buttonProps } = props;\n\n  const ref = React.useRef<HTMLButtonElement>(null);\n  React.useEffect(() => {\n    if (modifiers.focused) ref.current?.focus();\n  }, [modifiers.focused]);\n  return <button ref={ref} {...buttonProps} />;\n}\n\nexport type DayButtonProps = Parameters<typeof DayButton>[0];\n", "import React, { type SelectHTMLAttributes } from \"react\";\n\nimport { UI } from \"../UI.js\";\nimport type { ClassNames, CustomComponents } from \"../types/index.js\";\n\n/** An option to use in the dropdown. Maps to the `<option>` HTML element. */\nexport type DropdownOption = {\n  /** The value of the option. */\n  value: number;\n  /** The label of the option. */\n  label: string;\n  /** Whether the dropdown option is disabled (e.g., out of the calendar range). */\n  disabled: boolean;\n};\n\n/**\n * Render a dropdown component for navigation in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Dropdown(\n  props: {\n    /**\n     * @deprecated Use {@link useDayPicker} hook to get the list of internal\n     *   components.\n     */\n    components: CustomComponents;\n    /**\n     * @deprecated Use {@link useDayPicker} hook to get the list of internal\n     *   class names.\n     */\n    classNames: ClassNames;\n    /** The options to display in the dropdown. */\n    options?: DropdownOption[] | undefined;\n  } & Omit<SelectHTMLAttributes<HTMLSelectElement>, \"children\">\n) {\n  const { options, className, components, classNames, ...selectProps } = props;\n\n  const cssClassSelect = [classNames[UI.Dropdown], className].join(\" \");\n\n  const selectedOption = options?.find(\n    ({ value }) => value === selectProps.value\n  );\n  return (\n    <span\n      data-disabled={selectProps.disabled}\n      className={classNames[UI.DropdownRoot]}\n    >\n      <components.Select className={cssClassSelect} {...selectProps}>\n        {options?.map(({ value, label, disabled }) => (\n          <components.Option key={value} value={value} disabled={disabled}>\n            {label}\n          </components.Option>\n        ))}\n      </components.Select>\n      <span className={classNames[UI.CaptionLabel]} aria-hidden>\n        {selectedOption?.label}\n        <components.Chevron\n          orientation=\"down\"\n          size={18}\n          className={classNames[UI.Chevron]}\n        />\n      </span>\n    </span>\n  );\n}\n\nexport type DropdownProps = Parameters<typeof Dropdown>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render the navigation dropdowns for the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function DropdownNav(props: HTMLAttributes<HTMLDivElement>) {\n  return <div {...props} />;\n}\n\nexport type DropdownNavProps = Parameters<typeof DropdownNav>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render the footer of the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Footer(props: HTMLAttributes<HTMLDivElement>) {\n  return <div {...props} />;\n}\n\nexport type FooterProps = Parameters<typeof Footer>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\nimport type { CalendarMonth } from \"../classes/CalendarMonth.js\";\n\n/**\n * Render the grid with the weekday header row and the weeks for a specific\n * month.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Month(\n  props: {\n    /** The month to display in the grid. */\n    calendarMonth: CalendarMonth;\n    /** The index of the month being displayed. */\n    displayIndex: number;\n  } & HTMLAttributes<HTMLDivElement>\n) {\n  const { calendarMonth, displayIndex, ...divProps } = props;\n  return <div {...divProps}>{props.children}</div>;\n}\n\nexport type MonthProps = Parameters<typeof Month>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\nimport type { CalendarMonth } from \"../classes/index.js\";\n\n/**\n * Render the caption for a month in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function MonthCaption(\n  props: {\n    /** The month to display in the caption. */\n    calendarMonth: CalendarMonth;\n    /** The index of the month being displayed. */\n    displayIndex: number;\n  } & HTMLAttributes<HTMLDivElement>\n) {\n  const { calendarMonth, displayIndex, ...divProps } = props;\n  return <div {...divProps} />;\n}\n\nexport type MonthCaptionProps = Parameters<typeof MonthCaption>[0];\n", "import React, { type TableHTMLAttributes } from \"react\";\n\n/**\n * Render the grid of days for a specific month.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function MonthGrid(props: TableHTMLAttributes<HTMLTableElement>) {\n  return <table {...props} />;\n}\n\nexport type MonthGridProps = Parameters<typeof MonthGrid>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render a container wrapping the month grids.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Months(props: HTMLAttributes<HTMLDivElement>) {\n  return <div {...props} />;\n}\n\nexport type MonthsProps = Parameters<typeof Months>[0];\n", "import React from \"react\";\n\nimport { useDayPicker } from \"../useDayPicker.js\";\n\nimport { DropdownProps } from \"./Dropdown.js\";\n\n/**\n * Render a dropdown to navigate between months in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function MonthsDropdown(props: DropdownProps) {\n  const { components } = useDayPicker();\n  return <components.Dropdown {...props} />;\n}\n", "import { createContext, useContext } from \"react\";\n\nimport { CalendarDay } from \"./classes/CalendarDay.js\";\nimport { CalendarMonth } from \"./classes/CalendarMonth.js\";\nimport { DayPickerProps } from \"./types/props.js\";\nimport type { SelectedValue, SelectHandler } from \"./types/selection.js\";\nimport {\n  ClassNames,\n  CustomComponents,\n  Formatters,\n  Labels,\n  Mode,\n  Modifiers,\n  Styles\n} from \"./types/shared.js\";\n\n/** @ignore */\nexport const dayPickerContext = createContext<\n  | DayPickerContext<{\n      mode?: Mode | undefined;\n      required?: boolean | undefined;\n    }>\n  | undefined\n>(undefined);\n\n/**\n * Represents the context for the DayPicker component, providing various\n * properties and methods to interact with the calendar.\n *\n * @template T - The type of the DayPicker props, which must optionally include\n *   `mode` and `required` properties. This type can be used to refine the type\n *   returned by the hook.\n */\nexport type DayPickerContext<\n  T extends { mode?: Mode | undefined; required?: boolean | undefined }\n> = {\n  /** The months displayed in the calendar. */\n  months: CalendarMonth[];\n  /** The next month to display. */\n  nextMonth: Date | undefined;\n  /** The previous month to display. */\n  previousMonth: Date | undefined;\n  /** Navigate to the specified month. Will fire the `onMonthChange` callback. */\n  goToMonth: (month: Date) => void;\n  /** Returns the modifiers for the given day. */\n  getModifiers: (day: CalendarDay) => Modifiers;\n  /** The selected date(s). */\n  selected: SelectedValue<T> | undefined;\n  /** Set a selection. */\n  select: SelectHandler<T> | undefined;\n  /** Whether the given date is selected. */\n  isSelected: ((date: Date) => boolean) | undefined;\n  /** The components used internally by DayPicker. */\n  components: CustomComponents;\n  /** The class names for the UI elements. */\n  classNames: ClassNames;\n  /** The styles for the UI elements. */\n  styles: Partial<Styles> | undefined;\n  /** The labels used in the user interface. */\n  labels: Labels;\n  /** The formatters used to format the UI elements. */\n  formatters: Formatters;\n  /**\n   * The props as passed to the DayPicker component.\n   *\n   * @since 9.3.0\n   */\n  dayPickerProps: DayPickerProps;\n};\n\n/**\n * Provides access to the DayPicker context, which includes properties and\n * methods to interact with the DayPicker component. This hook must be used\n * within a custom component.\n *\n * @template T - Use this type to refine the returned context type with a\n *   specific selection mode.\n * @returns The context to work with DayPicker.\n * @throws {Error} If the hook is used outside of a DayPicker provider.\n * @group Hooks\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function useDayPicker<\n  T extends { mode?: Mode | undefined; required?: boolean | undefined }\n>(): DayPickerContext<T> {\n  const context = useContext(dayPickerContext);\n  if (context === undefined) {\n    throw new Error(\"useDayPicker() must be used within a custom component.\");\n  }\n  return context;\n}\n", "import React, {\n  type Mouse<PERSON>ventH<PERSON><PERSON>,\n  type HTMLAttributes,\n  useCallback\n} from \"react\";\n\nimport { UI } from \"../UI.js\";\nimport { useDayPicker } from \"../useDayPicker.js\";\n\n/**\n * Render the navigation toolbar with buttons to navigate between months.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Nav(\n  props: {\n    /** <PERSON><PERSON> for the previous month button click. */\n    onPreviousClick?: MouseEventHandler<HTMLButtonElement>;\n    /** Handler for the next month button click. */\n    onNextClick?: MouseEventHandler<HTMLButtonElement>;\n    /** The date of the previous month, if available. */\n    previousMonth?: Date | undefined;\n    /** The date of the next month, if available. */\n    nextMonth?: Date | undefined;\n  } & HTMLAttributes<HTMLElement>\n) {\n  const {\n    onPreviousClick,\n    onNextClick,\n    previousMonth,\n    nextMonth,\n    ...navProps\n  } = props;\n\n  const {\n    components,\n    classNames,\n    labels: { labelPrevious, labelNext }\n  } = useDayPicker();\n\n  const handleNextClick = useCallback(\n    (e: React.MouseEvent<HTMLButtonElement>) => {\n      if (nextMonth) {\n        onNextClick?.(e);\n      }\n    },\n    [nextMonth, onNextClick]\n  );\n\n  const handlePreviousClick = useCallback(\n    (e: React.MouseEvent<HTMLButtonElement>) => {\n      if (previousMonth) {\n        onPreviousClick?.(e);\n      }\n    },\n    [previousMonth, onPreviousClick]\n  );\n\n  return (\n    <nav {...navProps}>\n      <components.PreviousMonthButton\n        type=\"button\"\n        className={classNames[UI.PreviousMonthButton]}\n        tabIndex={previousMonth ? undefined : -1}\n        aria-disabled={previousMonth ? undefined : true}\n        aria-label={labelPrevious(previousMonth)}\n        onClick={handlePreviousClick}\n      >\n        <components.Chevron\n          disabled={previousMonth ? undefined : true}\n          className={classNames[UI.Chevron]}\n          orientation=\"left\"\n        />\n      </components.PreviousMonthButton>\n      <components.NextMonthButton\n        type=\"button\"\n        className={classNames[UI.NextMonthButton]}\n        tabIndex={nextMonth ? undefined : -1}\n        aria-disabled={nextMonth ? undefined : true}\n        aria-label={labelNext(nextMonth)}\n        onClick={handleNextClick}\n      >\n        <components.Chevron\n          disabled={nextMonth ? undefined : true}\n          orientation=\"right\"\n          className={classNames[UI.Chevron]}\n        />\n      </components.NextMonthButton>\n    </nav>\n  );\n}\n\nexport type NavProps = Parameters<typeof Nav>[0];\n", "import React, { type ButtonHTMLAttributes } from \"react\";\n\nimport { useDayPicker } from \"../useDayPicker.js\";\n\n/**\n * Render the button to navigate to the next month in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function NextMonthButton(\n  props: ButtonHTMLAttributes<HTMLButtonElement>\n) {\n  const { components } = useDayPicker();\n  return <components.Button {...props} />;\n}\n\nexport type NextMonthButtonProps = Parameters<typeof NextMonthButton>[0];\n", "import React, { type OptionHTMLAttributes } from \"react\";\n\n/**\n * Render an `option` element.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Option(props: OptionHTMLAttributes<HTMLOptionElement>) {\n  return <option {...props} />;\n}\n\nexport type OptionProps = Parameters<typeof Option>[0];\n", "import React, { type ButtonHTMLAttributes } from \"react\";\n\nimport { useDayPicker } from \"../useDayPicker.js\";\n\n/**\n * Render the button to navigate to the previous month in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function PreviousMonthButton(\n  props: ButtonHTMLAttributes<HTMLButtonElement>\n) {\n  const { components } = useDayPicker();\n  return <components.Button {...props} />;\n}\n\nexport type PreviousMonthButtonProps = Parameters<\n  typeof PreviousMonthButton\n>[0];\n", "import React, { type Ref, type HTMLAttributes } from \"react\";\n\n/**\n * Render the root element of the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Root(\n  props: {\n    /** Ref for the root element, used when `animate` is `true`. */\n    rootRef?: Ref<HTMLDivElement>;\n  } & HTMLAttributes<HTMLDivElement>\n) {\n  const { rootRef, ...rest } = props;\n  return <div {...rest} ref={rootRef} />;\n}\n\nexport type RootProps = Parameters<typeof Root>[0];\n", "import React, { type SelectHTMLAttributes } from \"react\";\n\n/**\n * Render a `select` element.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Select(props: SelectHTMLAttributes<HTMLSelectElement>) {\n  return <select {...props} />;\n}\n\nexport type SelectProps = Parameters<typeof Select>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\nimport type { CalendarWeek } from \"../classes/index.js\";\n\n/**\n * Render a table row representing a week in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Week(\n  props: {\n    /** The week to render. */\n    week: CalendarWeek;\n  } & HTMLAttributes<HTMLTableRowElement>\n) {\n  const { week, ...trProps } = props;\n  return <tr {...trProps} />;\n}\n\nexport type WeekProps = Parameters<typeof Week>[0];\n", "import React, { type ThHTMLAttributes } from \"react\";\n\n/**\n * Render a table header cell with the name of a weekday (e.g., \"Mo\", \"Tu\").\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Weekday(props: ThHTMLAttributes<HTMLTableCellElement>) {\n  return <th {...props} />;\n}\n\nexport type WeekdayProps = Parameters<typeof Weekday>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render the table row containing the weekday names.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Weekdays(props: HTMLAttributes<HTMLTableRowElement>) {\n  return (\n    <thead aria-hidden>\n      <tr {...props} />\n    </thead>\n  );\n}\n\nexport type WeekdaysProps = Parameters<typeof Weekdays>[0];\n", "import React, { type ThHTMLAttributes } from \"react\";\n\nimport type { CalendarWeek } from \"../classes/index.js\";\n\n/**\n * Render a table cell displaying the number of the week.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function WeekNumber(\n  props: {\n    /** The week to display. */\n    week: CalendarWeek;\n  } & ThHTMLAttributes<HTMLTableCellElement>\n) {\n  const { week, ...thProps } = props;\n  return <th {...thProps} />;\n}\n\nexport type WeekNumberProps = Parameters<typeof WeekNumber>[0];\n", "import React, { type ThHTMLAttributes } from \"react\";\n\n/**\n * Render the header cell for the week numbers column.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function WeekNumberHeader(\n  props: ThHTMLAttributes<HTMLTableCellElement>\n) {\n  return <th {...props} />;\n}\n\nexport type WeekNumberHeaderProps = Parameters<typeof WeekNumberHeader>[0];\n", "import React, { type HTMLAttributes } from \"react\";\n\n/**\n * Render the container for the weeks in the month grid.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function Weeks(props: HTMLAttributes<HTMLTableSectionElement>) {\n  return <tbody {...props} />;\n}\n\nexport type WeeksProps = Parameters<typeof Weeks>[0];\n", "import React from \"react\";\n\nimport { useDayPicker } from \"../useDayPicker.js\";\n\nimport { DropdownProps } from \"./Dropdown.js\";\n\n/**\n * Render a dropdown to navigate between years in the calendar.\n *\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport function YearsDropdown(props: DropdownProps) {\n  const { components } = useDayPicker();\n  return <components.Dropdown {...props} />;\n}\n", "import * as components from \"../components/custom-components.js\";\nimport type { CustomComponents, DayPickerProps } from \"../types/index.js\";\n\n/**\n * Merges custom components from the props with the default components.\n *\n * This function ensures that any custom components provided in the props\n * override the default components.\n *\n * @param customComponents The custom components provided in the DayPicker\n *   props.\n * @returns An object containing the merged components.\n */\nexport function getComponents(\n  customComponents: DayPickerProps[\"components\"]\n): CustomComponents {\n  return {\n    ...components,\n    ...customComponents\n  };\n}\n", "import type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Extracts `data-` attributes from the DayPicker props.\n *\n * This function collects all `data-` attributes from the props and adds\n * additional attributes based on the DayPicker configuration.\n *\n * @param props The DayPicker props.\n * @returns An object containing the `data-` attributes.\n */\nexport function getDataAttributes(\n  props: DayPickerProps\n): Record<string, unknown> {\n  const dataAttributes: Record<string, unknown> = {\n    \"data-mode\": props.mode ?? undefined,\n    \"data-required\": \"required\" in props ? props.required : undefined,\n    \"data-multiple-months\":\n      (props.numberOfMonths && props.numberOfMonths > 1) || undefined,\n    \"data-week-numbers\": props.showWeekNumber || undefined,\n    \"data-broadcast-calendar\": props.broadcastCalendar || undefined,\n    \"data-nav-layout\": props.navLayout || undefined\n  };\n  Object.entries(props).forEach(([key, val]) => {\n    if (key.startsWith(\"data-\")) {\n      dataAttributes[key] = val;\n    }\n  });\n  return dataAttributes;\n}\n", "import { UI, DayFlag, SelectionState, Animation } from \"../UI.js\";\nimport type { ClassNames } from \"../types/index.js\";\n\n/**\n * Returns the default class names for the UI elements.\n *\n * This function generates a mapping of default class names for various UI\n * elements, day flags, selection states, and animations.\n *\n * @returns An object containing the default class names.\n * @group Utilities\n */\nexport function getDefaultClassNames(): ClassNames {\n  const classNames: Partial<Required<ClassNames>> = {};\n\n  for (const key in UI) {\n    classNames[UI[key as keyof typeof UI]] =\n      `rdp-${UI[key as keyof typeof UI]}`;\n  }\n\n  for (const key in DayFlag) {\n    classNames[DayFlag[key as keyof typeof DayFlag]] =\n      `rdp-${DayFlag[key as keyof typeof DayFlag]}`;\n  }\n\n  for (const key in SelectionState) {\n    classNames[SelectionState[key as keyof typeof SelectionState]] =\n      `rdp-${SelectionState[key as keyof typeof SelectionState]}`;\n  }\n\n  for (const key in Animation) {\n    classNames[Animation[key as keyof typeof Animation]] =\n      `rdp-${Animation[key as keyof typeof Animation]}`;\n  }\n\n  return classNames as Required<ClassNames>;\n}\n", "export * from \"./formatCaption.js\";\nexport * from \"./formatDay.js\";\nexport * from \"./formatMonthDropdown.js\";\nexport * from \"./formatWeekNumber.js\";\nexport * from \"./formatWeekNumberHeader.js\";\nexport * from \"./formatWeekdayName.js\";\nexport * from \"./formatYearDropdown.js\";\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Formats the caption of the month.\n *\n * @defaultValue `LLLL y` (e.g., \"November 2022\").\n * @param month The date representing the month.\n * @param options Configuration options for the date library.\n * @param dateLib The date library to use for formatting. If not provided, a new\n *   instance is created.\n * @returns The formatted caption as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatCaption(\n  month: Date,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  return (dateLib ?? new DateLib(options)).format(month, \"LLLL y\");\n}\n\n/**\n * @private\n * @deprecated Use {@link formatCaption} instead.\n * @group Formatters\n */\nexport const formatMonthCaption = formatCaption;\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Formats the day date shown in the day cell.\n *\n * @defaultValue `d` (e.g., \"1\").\n * @param date The date to format.\n * @param options Configuration options for the date library.\n * @param dateLib The date library to use for formatting. If not provided, a new\n *   instance is created.\n * @returns The formatted day as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatDay(\n  date: Date,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  return (dateLib ?? new DateLib(options)).format(date, \"d\");\n}\n", "import { defaultDateLib, type DateLib } from \"../classes/DateLib.js\";\n\n/**\n * Formats the month for the dropdown option label.\n *\n * @defaultValue The localized full month name.\n * @param month The date representing the month.\n * @param dateLib The date library to use for formatting. Defaults to\n *   `defaultDateLib`.\n * @returns The formatted month name as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatMonthDropdown(\n  month: Date,\n  dateLib: DateLib = defaultDateLib\n): string {\n  return dateLib.format(month, \"LLLL\");\n}\n", "import { defaultDateLib } from \"../classes/DateLib.js\";\n\n/**\n * Formats the week number.\n *\n * @defaultValue The week number as a string, with a leading zero for single-digit numbers.\n * @param weekNumber The week number to format.\n * @param dateLib The date library to use for formatting. Defaults to\n *   `defaultDateLib`.\n * @returns The formatted week number as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatWeekNumber(weekNumber: number, dateLib = defaultDateLib) {\n  if (weekNumber < 10) {\n    return dateLib.formatNumber(`0${weekNumber.toLocaleString()}`);\n  }\n  return dateLib.formatNumber(`${weekNumber.toLocaleString()}`);\n}\n", "/**\n * Formats the header for the week number column.\n *\n * @defaultValue An empty string `\"\"`.\n * @returns The formatted week number header as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatWeekNumberHeader() {\n  return ``;\n}\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Formats the name of a weekday to be displayed in the weekdays header.\n *\n * @defaultValue `cccccc` (e.g., \"Mo\" for Monday).\n * @param weekday The date representing the weekday.\n * @param options Configuration options for the date library.\n * @param dateLib The date library to use for formatting. If not provided, a new\n *   instance is created.\n * @returns The formatted weekday name as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatWeekdayName(\n  weekday: Date,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  return (dateLib ?? new DateLib(options)).format(weekday, \"cccccc\");\n}\n", "import { defaultDateLib, type DateLib } from \"../classes/DateLib.js\";\n\n/**\n * Formats the year for the dropdown option label.\n *\n * @param year The year to format.\n * @param dateLib The date library to use for formatting. Defaults to\n *   `defaultDateLib`.\n * @returns The formatted year as a string.\n * @group Formatters\n * @see https://daypicker.dev/docs/translation#custom-formatters\n */\nexport function formatYearDropdown(\n  year: Date,\n  dateLib: DateLib = defaultDateLib\n): string {\n  return dateLib.format(year, \"yyyy\");\n}\n\n/**\n * @private\n * @deprecated Use `formatYearDropdown` instead.\n * @group Formatters\n */\nexport const formatYearCaption = formatYearDropdown;\n", "import * as defaultFormatters from \"../formatters/index.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Merges custom formatters from the props with the default formatters.\n *\n * @param customFormatters The custom formatters provided in the DayPicker\n *   props.\n * @returns The merged formatters object.\n */\nexport function getFormatters(customFormatters: DayPickerProps[\"formatters\"]) {\n  if (customFormatters?.formatMonthCaption && !customFormatters.formatCaption) {\n    customFormatters.formatCaption = customFormatters.formatMonthCaption;\n  }\n  if (\n    customFormatters?.formatYearCaption &&\n    !customFormatters.formatYearDropdown\n  ) {\n    customFormatters.formatYearDropdown = customFormatters.formatYearCaption;\n  }\n  return {\n    ...defaultFormatters,\n    ...customFormatters\n  };\n}\n", "import { type DateLib } from \"../classes/DateLib.js\";\nimport { DropdownOption } from \"../components/Dropdown.js\";\nimport type { Formatters } from \"../types/index.js\";\n\n/**\n * Returns the months to show in the dropdown.\n *\n * This function generates a list of months for the current year, formatted\n * using the provided formatter, and determines whether each month should be\n * disabled based on the navigation range.\n *\n * @param displayMonth The currently displayed month.\n * @param navStart The start date for navigation.\n * @param navEnd The end date for navigation.\n * @param formatters The formatters to use for formatting the month labels.\n * @param dateLib The date library to use for date manipulation.\n * @returns An array of dropdown options representing the months, or `undefined`\n *   if no months are available.\n */\nexport function getMonthOptions(\n  displayMonth: Date,\n  navStart: Date | undefined,\n  navEnd: Date | undefined,\n  formatters: Pick<Formatters, \"formatMonthDropdown\">,\n  dateLib: DateLib\n): DropdownOption[] | undefined {\n  const {\n    startOfMonth,\n    startOfYear,\n    endOfYear,\n    eachMonthOfInterval,\n    getMonth\n  } = dateLib;\n\n  const months = eachMonthOfInterval({\n    start: startOfYear(displayMonth),\n    end: endOfYear(displayMonth)\n  });\n\n  const options = months.map((month) => {\n    const label = formatters.formatMonthDropdown(month, dateLib);\n    const value = getMonth(month);\n    const disabled =\n      (navStart && month < startOfMonth(navStart)) ||\n      (navEnd && month > startOfMonth(navEnd)) ||\n      false;\n    return { value, label, disabled };\n  });\n\n  return options;\n}\n", "import type { CSSProperties } from \"react\";\n\nimport { UI } from \"../UI.js\";\nimport type { Modifiers, ModifiersStyles, Styles } from \"../types/index.js\";\n\n/**\n * Returns the computed style for a day based on its modifiers.\n *\n * This function merges the base styles for the day with any styles associated\n * with active modifiers.\n *\n * @param dayModifiers The modifiers applied to the day.\n * @param styles The base styles for the calendar elements.\n * @param modifiersStyles The styles associated with specific modifiers.\n * @returns The computed style for the day.\n */\nexport function getStyleForModifiers(\n  dayModifiers: Modifiers,\n  styles: Partial<Styles> = {},\n  modifiersStyles: Partial<ModifiersStyles> = {}\n): CSSProperties {\n  let style: CSSProperties = { ...styles?.[UI.Day] };\n  Object.entries(dayModifiers)\n    .filter(([, active]) => active === true)\n    .forEach(([modifier]) => {\n      style = {\n        ...style,\n        ...modifiersStyles?.[modifier]\n      };\n    });\n  return style;\n}\n", "import { DateLib } from \"../classes/DateLib.js\";\n\n/**\n * Generates a series of 7 days, starting from the beginning of the week, to use\n * for formatting weekday names (e.g., Monday, Tuesday, etc.).\n *\n * @param dateLib The date library to use for date manipulation.\n * @param ISOWeek Whether to use ISO week numbering (weeks start on Monday).\n * @param broadcastCalendar Whether to use the broadcast calendar (weeks start\n *   on Monday, but may include adjustments for broadcast-specific rules).\n * @returns An array of 7 dates representing the weekdays.\n */\nexport function getWeekdays(\n  dateLib: DateLib,\n  ISOWeek?: boolean | undefined,\n  broadcastCalendar?: boolean | undefined\n): Date[] {\n  const today = dateLib.today();\n\n  const start = broadcastCalendar\n    ? dateLib.startOfBroadcastWeek(today, dateLib)\n    : ISOWeek\n      ? dateLib.startOfISOWeek(today)\n      : dateLib.startOfWeek(today);\n\n  const days: Date[] = [];\n  for (let i = 0; i < 7; i++) {\n    const day = dateLib.addDays(start, i);\n    days.push(day);\n  }\n  return days;\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport { DropdownOption } from \"../components/Dropdown.js\";\nimport type { Formatters } from \"../types/index.js\";\n\n/**\n * Returns the years to display in the dropdown.\n *\n * This function generates a list of years between the navigation start and end\n * dates, formatted using the provided formatter.\n *\n * @param navStart The start date for navigation.\n * @param navEnd The end date for navigation.\n * @param formatters The formatters to use for formatting the year labels.\n * @param dateLib The date library to use for date manipulation.\n * @returns An array of dropdown options representing the years, or `undefined`\n *   if `navStart` or `navEnd` is not provided.\n */\nexport function getYearOptions(\n  navStart: Date | undefined,\n  navEnd: Date | undefined,\n  formatters: Pick<Formatters, \"formatYearDropdown\">,\n  dateLib: DateLib\n): DropdownOption[] | undefined {\n  if (!navStart) return undefined;\n  if (!navEnd) return undefined;\n  const { startOfYear, endOfYear, addYears, getYear, isBefore, isSameYear } =\n    dateLib;\n  const firstNavYear = startOfYear(navStart);\n  const lastNavYear = endOfYear(navEnd);\n  const years: Date[] = [];\n\n  let year = firstNavYear;\n  while (isBefore(year, lastNavYear) || isSameYear(year, lastNavYear)) {\n    years.push(year);\n    year = addYears(year, 1);\n  }\n\n  return years.map((year) => {\n    const label = formatters.formatYearDropdown(year, dateLib);\n    return {\n      value: getYear(year),\n      label,\n      disabled: false\n    };\n  });\n}\n", "export * from \"./labelGrid.js\";\nexport * from \"./labelGridcell.js\";\nexport * from \"./labelDayButton.js\";\nexport * from \"./labelNav.js\";\nexport * from \"./labelGrid.js\";\nexport * from \"./labelMonthDropdown.js\";\nexport * from \"./labelNext.js\";\nexport * from \"./labelPrevious.js\";\nexport * from \"./labelWeekday.js\";\nexport * from \"./labelWeekNumber.js\";\nexport * from \"./labelWeekNumberHeader.js\";\nexport * from \"./labelYearDropdown.js\";\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for the month grid, which is announced when entering\n * the grid.\n *\n * @defaultValue `LLLL y` (e.g., \"November 2022\").\n * @param date - The date representing the month.\n * @param options - Optional configuration for the date formatting library.\n * @param dateLib - An optional instance of the date formatting library.\n * @returns The ARIA label for the month grid.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelGrid(\n  date: Date,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  return (dateLib ?? new DateLib(options)).format(date, \"LLLL y\");\n}\n\n/**\n * @ignore\n * @deprecated Use {@link labelGrid} instead.\n */\nexport const labelCaption = labelGrid;\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\nimport type { Modifiers } from \"../types/index.js\";\n\n/**\n * Generates the label for a day grid cell when the calendar is not interactive.\n *\n * @param date - The date to format.\n * @param modifiers - Optional modifiers providing context for the day.\n * @param options - Optional configuration for the date formatting library.\n * @param dateLib - An optional instance of the date formatting library.\n * @returns The label for the day grid cell.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelGridcell(\n  date: Date,\n  modifiers?: Modifiers,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  let label = (dateLib ?? new DateLib(options)).format(date, \"PPPP\");\n  if (modifiers?.today) {\n    label = `Today, ${label}`;\n  }\n  return label;\n}\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\nimport type { Modifiers } from \"../types/index.js\";\n\n/**\n * Generates the ARIA label for a day button.\n *\n * Use the `modifiers` argument to provide additional context for the label,\n * such as indicating if the day is \"today\" or \"selected.\"\n *\n * @defaultValue The formatted date.\n * @param date - The date to format.\n * @param modifiers - The modifiers providing context for the day.\n * @param options - Optional configuration for the date formatting library.\n * @param dateLib - An optional instance of the date formatting library.\n * @returns The ARIA label for the day button.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelDayButton(\n  date: Date,\n  modifiers: Modifiers,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n) {\n  let label = (dateLib ?? new DateLib(options)).format(date, \"PPPP\");\n  if (modifiers.today) label = `Today, ${label}`;\n  if (modifiers.selected) label = `${label}, selected`;\n  return label;\n}\n\n/**\n * @ignore\n * @deprecated Use `labelDayButton` instead.\n */\nexport const labelDay = labelDayButton;\n", "/**\n * Generates the ARIA label for the navigation toolbar.\n *\n * @defaultValue `\"\"`\n * @returns The ARIA label for the navigation toolbar.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelNav(): string {\n  return \"\";\n}\n", "import type { DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for the months dropdown.\n *\n * @defaultValue `\"Choose the Month\"`\n * @param options - Optional configuration for the date formatting library.\n * @returns The ARIA label for the months dropdown.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelMonthDropdown(options?: DateLibOptions) {\n  return \"Choose the Month\";\n}\n", "/**\n * Generates the ARIA label for the \"next month\" button.\n *\n * @defaultValue `\"Go to the Next Month\"`\n * @param month - The date representing the next month, or `undefined` if there\n *   is no next month.\n * @returns The ARIA label for the \"next month\" button.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelNext(month: Date | undefined) {\n  return \"Go to the Next Month\";\n}\n", "/**\n * Generates the ARIA label for the \"previous month\" button.\n *\n * @defaultValue `\"Go to the Previous Month\"`\n * @param month - The date representing the previous month, or `undefined` if\n *   there is no previous month.\n * @returns The ARIA label for the \"previous month\" button.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelPrevious(month: Date | undefined) {\n  return \"Go to the Previous Month\";\n}\n", "import { DateLib, type DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for a weekday column header.\n *\n * @defaultValue `\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\", \"Sunday\"`\n * @param date - The date representing the weekday.\n * @param options - Optional configuration for the date formatting library.\n * @param dateLib - An optional instance of the date formatting library.\n * @returns The ARIA label for the weekday column header.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelWeekday(\n  date: Date,\n  options?: DateLibOptions,\n  dateLib?: DateLib\n): string {\n  return (dateLib ?? new DateLib(options)).format(date, \"cccc\");\n}\n", "import type { DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for the week number cell (the first cell in a row).\n *\n * @defaultValue `Week ${weekNumber}`\n * @param weekNumber - The number of the week.\n * @param options - Optional configuration for the date formatting library.\n * @returns The ARIA label for the week number cell.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelWeekNumber(\n  weekNumber: number,\n  options?: DateLibOptions\n): string {\n  return `Week ${weekNumber}`;\n}\n", "import type { DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for the week number header element.\n *\n * @defaultValue `\"Week Number\"`\n * @param options - Optional configuration for the date formatting library.\n * @returns The ARIA label for the week number header.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelWeekNumberHeader(options?: DateLibOptions): string {\n  return \"Week Number\";\n}\n", "import type { DateLibOptions } from \"../classes/DateLib.js\";\n\n/**\n * Generates the ARIA label for the years dropdown.\n *\n * @defaultValue `\"Choose the Year\"`\n * @param options - Optional configuration for the date formatting library.\n * @returns The ARIA label for the years dropdown.\n * @group Labels\n * @see https://daypicker.dev/docs/translation#aria-labels\n */\nexport function labelYearDropdown(options?: DateLibOptions) {\n  return \"Choose the Year\";\n}\n", "import React, { useLayoutEffect, useRef } from \"react\";\n\nimport { Animation } from \"./UI.js\";\nimport type { CalendarDay } from \"./classes/CalendarDay.js\";\nimport { CalendarMonth } from \"./classes/CalendarMonth.js\";\nimport type { DateLib } from \"./classes/DateLib.js\";\nimport { ClassNames } from \"./types/shared.js\";\n\nconst asHtmlElement = (element: Element | null): HTMLElement | null => {\n  if (element instanceof HTMLElement) return element;\n  return null;\n};\n\nconst queryMonthEls = (element: HTMLElement) => [\n  ...(element.querySelectorAll(\"[data-animated-month]\") ?? [])\n];\nconst queryMonthEl = (element: HTMLElement) =>\n  asHtmlElement(element.querySelector(\"[data-animated-month]\"));\nconst queryCaptionEl = (element: HTMLElement) =>\n  asHtmlElement(element.querySelector(\"[data-animated-caption]\"));\nconst queryWeeksEl = (element: HTMLElement) =>\n  asHtmlElement(element.querySelector(\"[data-animated-weeks]\"));\nconst queryNavEl = (element: HTMLElement) =>\n  asHtmlElement(element.querySelector(\"[data-animated-nav]\"));\nconst queryWeekdaysEl = (element: HTMLElement) =>\n  asHtmlElement(element.querySelector(\"[data-animated-weekdays]\"));\n\n/**\n * Handles animations for transitioning between months in the DayPicker\n * component.\n *\n * @private\n * @param rootElRef - A reference to the root element of the DayPicker\n *   component.\n * @param enabled - Whether animations are enabled.\n * @param options - Configuration options for the animation, including class\n *   names, months, focused day, and the date utility library.\n */\nexport function useAnimation(\n  rootElRef: React.RefObject<HTMLDivElement | null>,\n  enabled: boolean,\n  {\n    classNames,\n    months,\n    focused,\n    dateLib\n  }: {\n    classNames: ClassNames;\n    months: CalendarMonth[];\n    focused: CalendarDay | undefined;\n    dateLib: DateLib;\n  }\n): void {\n  const previousRootElSnapshotRef = useRef<HTMLElement>(null);\n  const previousMonthsRef = useRef(months);\n  const animatingRef = useRef(false);\n\n  useLayoutEffect(() => {\n    // get previous months before updating the previous months ref\n    const previousMonths = previousMonthsRef.current;\n    // update previous months ref for next effect trigger\n    previousMonthsRef.current = months;\n\n    if (\n      !enabled ||\n      !rootElRef.current ||\n      // safety check because the ref can be set to anything by consumers\n      !(rootElRef.current instanceof HTMLElement) ||\n      // validation required for the animation to work as expected\n      months.length === 0 ||\n      previousMonths.length === 0 ||\n      months.length !== previousMonths.length\n    ) {\n      return;\n    }\n\n    const isSameMonth = dateLib.isSameMonth(\n      months[0].date,\n      previousMonths[0].date\n    );\n\n    const isAfterPreviousMonth = dateLib.isAfter(\n      months[0].date,\n      previousMonths[0].date\n    );\n\n    const captionAnimationClass = isAfterPreviousMonth\n      ? classNames[Animation.caption_after_enter]\n      : classNames[Animation.caption_before_enter];\n\n    const weeksAnimationClass = isAfterPreviousMonth\n      ? classNames[Animation.weeks_after_enter]\n      : classNames[Animation.weeks_before_enter];\n\n    // get previous root element snapshot before updating the snapshot ref\n    const previousRootElSnapshot = previousRootElSnapshotRef.current;\n\n    // update snapshot for next effect trigger\n    const rootElSnapshot = rootElRef.current.cloneNode(true);\n    if (rootElSnapshot instanceof HTMLElement) {\n      // if this effect is triggered while animating, we need to clean up the new root snapshot\n      // to put it in the same state as when not animating, to correctly animate the next month change\n      const currentMonthElsSnapshot = queryMonthEls(rootElSnapshot);\n      currentMonthElsSnapshot.forEach((currentMonthElSnapshot) => {\n        if (!(currentMonthElSnapshot instanceof HTMLElement)) return;\n\n        // remove the old month snapshots from the new root snapshot\n        const previousMonthElSnapshot = queryMonthEl(currentMonthElSnapshot);\n        if (\n          previousMonthElSnapshot &&\n          currentMonthElSnapshot.contains(previousMonthElSnapshot)\n        ) {\n          currentMonthElSnapshot.removeChild(previousMonthElSnapshot);\n        }\n\n        // remove animation classes from the new month snapshots\n        const captionEl = queryCaptionEl(currentMonthElSnapshot);\n        if (captionEl) {\n          captionEl.classList.remove(captionAnimationClass);\n        }\n\n        const weeksEl = queryWeeksEl(currentMonthElSnapshot);\n        if (weeksEl) {\n          weeksEl.classList.remove(weeksAnimationClass);\n        }\n      });\n\n      previousRootElSnapshotRef.current = rootElSnapshot;\n    } else {\n      previousRootElSnapshotRef.current = null;\n    }\n\n    if (\n      animatingRef.current ||\n      isSameMonth ||\n      // skip animation if a day is focused because it can cause issues to the animation and is better for a11y\n      focused\n    ) {\n      return;\n    }\n\n    const previousMonthEls =\n      previousRootElSnapshot instanceof HTMLElement\n        ? queryMonthEls(previousRootElSnapshot)\n        : [];\n\n    const currentMonthEls = queryMonthEls(rootElRef.current);\n\n    if (\n      currentMonthEls &&\n      currentMonthEls.every((el) => el instanceof HTMLElement) &&\n      previousMonthEls &&\n      previousMonthEls.every((el) => el instanceof HTMLElement)\n    ) {\n      animatingRef.current = true;\n      const cleanUpFunctions: (() => void)[] = [];\n\n      // set isolation to isolate to isolate the stacking context during animation\n      rootElRef.current.style.isolation = \"isolate\";\n      // set z-index to 1 to ensure the nav is clickable over the other elements being animated\n      const navEl = queryNavEl(rootElRef.current);\n      if (navEl) {\n        navEl.style.zIndex = \"1\";\n      }\n\n      currentMonthEls.forEach((currentMonthEl, index) => {\n        const previousMonthEl = previousMonthEls[index];\n\n        if (!previousMonthEl) {\n          return;\n        }\n\n        // animate new displayed month\n        currentMonthEl.style.position = \"relative\";\n        currentMonthEl.style.overflow = \"hidden\";\n        const captionEl = queryCaptionEl(currentMonthEl);\n        if (captionEl) {\n          captionEl.classList.add(captionAnimationClass);\n        }\n\n        const weeksEl = queryWeeksEl(currentMonthEl);\n        if (weeksEl) {\n          weeksEl.classList.add(weeksAnimationClass);\n        }\n        // animate new displayed month end\n\n        const cleanUp = () => {\n          animatingRef.current = false;\n\n          if (rootElRef.current) {\n            rootElRef.current.style.isolation = \"\";\n          }\n          if (navEl) {\n            navEl.style.zIndex = \"\";\n          }\n\n          if (captionEl) {\n            captionEl.classList.remove(captionAnimationClass);\n          }\n          if (weeksEl) {\n            weeksEl.classList.remove(weeksAnimationClass);\n          }\n          currentMonthEl.style.position = \"\";\n          currentMonthEl.style.overflow = \"\";\n          if (currentMonthEl.contains(previousMonthEl)) {\n            currentMonthEl.removeChild(previousMonthEl);\n          }\n        };\n        cleanUpFunctions.push(cleanUp);\n\n        // animate old displayed month\n        previousMonthEl.style.pointerEvents = \"none\";\n        previousMonthEl.style.position = \"absolute\";\n        previousMonthEl.style.overflow = \"hidden\";\n        previousMonthEl.setAttribute(\"aria-hidden\", \"true\");\n\n        // hide the weekdays container of the old month and only the new one\n        const previousWeekdaysEl = queryWeekdaysEl(previousMonthEl);\n        if (previousWeekdaysEl) {\n          previousWeekdaysEl.style.opacity = \"0\";\n        }\n\n        const previousCaptionEl = queryCaptionEl(previousMonthEl);\n        if (previousCaptionEl) {\n          previousCaptionEl.classList.add(\n            isAfterPreviousMonth\n              ? classNames[Animation.caption_before_exit]\n              : classNames[Animation.caption_after_exit]\n          );\n          previousCaptionEl.addEventListener(\"animationend\", cleanUp);\n        }\n\n        const previousWeeksEl = queryWeeksEl(previousMonthEl);\n        if (previousWeeksEl) {\n          previousWeeksEl.classList.add(\n            isAfterPreviousMonth\n              ? classNames[Animation.weeks_before_exit]\n              : classNames[Animation.weeks_after_exit]\n          );\n        }\n\n        currentMonthEl.insertBefore(previousMonthEl, currentMonthEl.firstChild);\n      });\n    }\n  });\n}\n", "import { useEffect } from \"react\";\n\nimport type {\n  CalendarWeek,\n  CalendarDay,\n  CalendarMonth,\n  DateLib\n} from \"./classes/index.js\";\nimport { getDates } from \"./helpers/getDates.js\";\nimport { getDays } from \"./helpers/getDays.js\";\nimport { getDisplayMonths } from \"./helpers/getDisplayMonths.js\";\nimport { getInitialMonth } from \"./helpers/getInitialMonth.js\";\nimport { getMonths } from \"./helpers/getMonths.js\";\nimport { getNavMonths } from \"./helpers/getNavMonth.js\";\nimport { getNextMonth } from \"./helpers/getNextMonth.js\";\nimport { getPreviousMonth } from \"./helpers/getPreviousMonth.js\";\nimport { getWeeks } from \"./helpers/getWeeks.js\";\nimport { useControlledValue } from \"./helpers/useControlledValue.js\";\nimport type { DayPickerProps } from \"./types/props.js\";\n\n/**\n * Return the calendar object to work with the calendar in custom components.\n *\n * @see https://daypicker.dev/guides/custom-components\n */\nexport interface Calendar {\n  /**\n   * All the days displayed in the calendar. As opposite from\n   * {@link CalendarContext.dates}, it may return duplicated dates when shown\n   * outside the month.\n   */\n  days: CalendarDay[];\n  /** The months displayed in the calendar. */\n  weeks: CalendarWeek[];\n  /** The months displayed in the calendar. */\n  months: CalendarMonth[];\n\n  /** The next month to display. */\n  nextMonth: Date | undefined;\n  /** The previous month to display. */\n  previousMonth: Date | undefined;\n\n  /**\n   * The month where the navigation starts. `undefined` if the calendar can be\n   * navigated indefinitely to the past.\n   */\n  navStart: Date | undefined;\n  /**\n   * The month where the navigation ends. `undefined` if the calendar can be\n   * navigated indefinitely to the past.\n   */\n  navEnd: Date | undefined;\n\n  /** Navigate to the specified month. Will fire the `onMonthChange` callback. */\n  goToMonth: (month: Date) => void;\n  /**\n   * Navigate to the specified date. If the second parameter (refDate) is\n   * provided and the date is before the refDate, then the month is set to one\n   * month before the date.\n   *\n   * @param day - The date to navigate to.\n   * @param dateToCompare - Optional. If `date` is before `dateToCompare`, the\n   *   month is set to one month before the date.\n   */\n  goToDay: (day: CalendarDay) => void;\n}\n\n/**\n * Provides the calendar object to work with the calendar in custom components.\n *\n * @private\n * @param props - The DayPicker props related to calendar configuration.\n * @param dateLib - The date utility library instance.\n * @returns The calendar object containing displayed days, weeks, months, and\n *   navigation methods.\n */\nexport function useCalendar(\n  props: Pick<\n    DayPickerProps,\n    | \"captionLayout\"\n    | \"endMonth\"\n    | \"startMonth\"\n    | \"today\"\n    | \"fixedWeeks\"\n    | \"ISOWeek\"\n    | \"numberOfMonths\"\n    | \"disableNavigation\"\n    | \"onMonthChange\"\n    | \"month\"\n    | \"defaultMonth\"\n    | \"timeZone\"\n    | \"broadcastCalendar\"\n    // Deprecated:\n    | \"fromMonth\"\n    | \"fromYear\"\n    | \"toMonth\"\n    | \"toYear\"\n  >,\n  dateLib: DateLib\n): Calendar {\n  const [navStart, navEnd] = getNavMonths(props, dateLib);\n\n  const { startOfMonth, endOfMonth } = dateLib;\n  const initialMonth = getInitialMonth(props, navStart, navEnd, dateLib);\n  const [firstMonth, setFirstMonth] = useControlledValue(\n    initialMonth,\n    // initialMonth is always computed from props.month if provided\n    props.month ? initialMonth : undefined\n  );\n\n  useEffect(() => {\n    const newInitialMonth = getInitialMonth(props, navStart, navEnd, dateLib);\n    setFirstMonth(newInitialMonth);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [props.timeZone]);\n\n  /** The months displayed in the calendar. */\n  const displayMonths = getDisplayMonths(firstMonth, navEnd, props, dateLib);\n\n  /** The dates displayed in the calendar. */\n  const dates = getDates(\n    displayMonths,\n    props.endMonth ? endOfMonth(props.endMonth) : undefined,\n    props,\n    dateLib\n  );\n\n  /** The Months displayed in the calendar. */\n  const months = getMonths(displayMonths, dates, props, dateLib);\n\n  /** The Weeks displayed in the calendar. */\n  const weeks = getWeeks(months);\n\n  /** The Days displayed in the calendar. */\n  const days = getDays(months);\n\n  const previousMonth = getPreviousMonth(firstMonth, navStart, props, dateLib);\n  const nextMonth = getNextMonth(firstMonth, navEnd, props, dateLib);\n\n  const { disableNavigation, onMonthChange } = props;\n\n  const isDayInCalendar = (day: CalendarDay) =>\n    weeks.some((week: CalendarWeek) => week.days.some((d) => d.isEqualTo(day)));\n\n  const goToMonth = (date: Date) => {\n    if (disableNavigation) {\n      return;\n    }\n    let newMonth = startOfMonth(date);\n    // if month is before start, use the first month instead\n    if (navStart && newMonth < startOfMonth(navStart)) {\n      newMonth = startOfMonth(navStart);\n    }\n    // if month is after endMonth, use the last month instead\n    if (navEnd && newMonth > startOfMonth(navEnd)) {\n      newMonth = startOfMonth(navEnd);\n    }\n    setFirstMonth(newMonth);\n    onMonthChange?.(newMonth);\n  };\n\n  const goToDay = (day: CalendarDay) => {\n    // is this check necessary?\n    if (isDayInCalendar(day)) {\n      return;\n    }\n    goToMonth(day.date);\n  };\n\n  const calendar = {\n    months,\n    weeks,\n    days,\n\n    navStart,\n    navEnd,\n\n    previousMonth,\n    nextMonth,\n\n    goToMonth,\n    goToDay\n  };\n\n  return calendar;\n}\n", "import { type DateLib } from \"../classes/DateLib.js\";\nimport { type DayPickerProps } from \"../types/props.js\";\n\n/**\n * Returns all the dates to display in the calendar.\n *\n * This function calculates the range of dates to display based on the provided\n * display months, constraints, and calendar configuration.\n *\n * @param displayMonths The months to display in the calendar.\n * @param maxDate The maximum date to include in the range.\n * @param props The DayPicker props, including calendar configuration options.\n * @param dateLib The date library to use for date manipulation.\n * @returns An array of dates to display in the calendar.\n */\nexport function getDates(\n  displayMonths: Date[],\n  maxDate: Date | undefined,\n  props: Pick<DayPickerProps, \"ISOWeek\" | \"fixedWeeks\" | \"broadcastCalendar\">,\n  dateLib: DateLib\n): Date[] {\n  const firstMonth = displayMonths[0];\n  const lastMonth = displayMonths[displayMonths.length - 1];\n\n  const { ISOWeek, fixedWeeks, broadcastCalendar } = props ?? {};\n  const {\n    addDays,\n    differenceInCalendarDays,\n    differenceInCalendarMonths,\n    endOfBroadcastWeek,\n    endOfISOWeek,\n    endOfMonth,\n    endOfWeek,\n    isAfter,\n    startOfBroadcastWeek,\n    startOfISOWeek,\n    startOfWeek\n  } = dateLib;\n\n  const startWeekFirstDate = broadcastCalendar\n    ? startOfBroadcastWeek(firstMonth, dateLib)\n    : ISOWeek\n      ? startOfISOWeek(firstMonth)\n      : startOfWeek(firstMonth);\n\n  const endWeekLastDate = broadcastCalendar\n    ? endOfBroadcastWeek(lastMonth)\n    : ISOWeek\n      ? endOfISOWeek(endOfMonth(lastMonth))\n      : endOfWeek(endOfMonth(lastMonth));\n\n  const nOfDays = differenceInCalendarDays(endWeekLastDate, startWeekFirstDate);\n  const nOfMonths = differenceInCalendarMonths(lastMonth, firstMonth) + 1;\n\n  const dates: Date[] = [];\n  for (let i = 0; i <= nOfDays; i++) {\n    const date = addDays(startWeekFirstDate, i);\n    if (maxDate && isAfter(date, maxDate)) {\n      break;\n    }\n    dates.push(date);\n  }\n\n  // If fixed weeks is enabled, add the extra dates to the array\n  const nrOfDaysWithFixedWeeks = broadcastCalendar ? 35 : 42;\n  const extraDates = nrOfDaysWithFixedWeeks * nOfMonths;\n  if (fixedWeeks && dates.length < extraDates) {\n    const daysToAdd = extraDates - dates.length;\n    for (let i = 0; i < daysToAdd; i++) {\n      const date = addDays(dates[dates.length - 1], 1);\n      dates.push(date);\n    }\n  }\n  return dates;\n}\n", "import type { CalendarDay, CalendarMonth } from \"../classes/index.js\";\n\n/**\n * Returns all the days belonging to the calendar by merging the days in the\n * weeks for each month.\n *\n * @param calendarMonths The array of calendar months.\n * @returns An array of `CalendarDay` objects representing all the days in the\n *   calendar.\n */\nexport function getDays(calendarMonths: CalendarMonth[]) {\n  const initialDays: CalendarDay[] = [];\n  return calendarMonths.reduce((days, month) => {\n    const weekDays: CalendarDay[] = month.weeks.reduce((weekDays, week) => {\n      return [...weekDays, ...week.days];\n    }, initialDays);\n    return [...days, ...weekDays];\n  }, initialDays);\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Returns the months to display in the calendar.\n *\n * @param firstDisplayedMonth The first month currently displayed in the\n *   calendar.\n * @param calendarEndMonth The latest month the user can navigate to.\n * @param props The DayPicker props, including `numberOfMonths`.\n * @param dateLib The date library to use for date manipulation.\n * @returns An array of dates representing the months to display.\n */\nexport function getDisplayMonths(\n  firstDisplayedMonth: Date,\n  calendarEndMonth: Date | undefined,\n  props: Pick<DayPickerProps, \"numberOfMonths\">,\n  dateLib: DateLib\n): Date[] {\n  const { numberOfMonths = 1 } = props;\n  const months: Date[] = [];\n  for (let i = 0; i < numberOfMonths; i++) {\n    const month = dateLib.addMonths(firstDisplayedMonth, i);\n    if (calendarEndMonth && month > calendarEndMonth) {\n      break;\n    }\n    months.push(month);\n  }\n  return months;\n}\n", "import { type DateLib } from \"../classes/DateLib.js\";\nimport { type DayPickerProps } from \"../types/props.js\";\n\n/**\n * Determines the initial month to display in the calendar based on the provided\n * props.\n *\n * This function calculates the starting month, considering constraints such as\n * `startMonth`, `endMonth`, and the number of months to display.\n *\n * @param props The DayPicker props, including navigation and date constraints.\n * @param dateLib The date library to use for date manipulation.\n * @returns The initial month to display.\n */\nexport function getInitialMonth(\n  props: Pick<\n    DayPickerProps,\n    | \"fromYear\"\n    | \"toYear\"\n    | \"month\"\n    | \"defaultMonth\"\n    | \"today\"\n    | \"numberOfMonths\"\n    | \"timeZone\"\n  >,\n  navStart: Date | undefined,\n  navEnd: Date | undefined,\n  dateLib: DateLib\n): Date {\n  const {\n    month,\n    defaultMonth,\n    today = dateLib.today(),\n    numberOfMonths = 1\n  } = props;\n  let initialMonth = month || defaultMonth || today;\n  const { differenceInCalendarMonths, addMonths, startOfMonth } = dateLib;\n\n  if (\n    navEnd &&\n    differenceInCalendarMonths(navEnd, initialMonth) < numberOfMonths - 1\n  ) {\n    const offset = -1 * (numberOfMonths - 1);\n    initialMonth = addMonths(navEnd, offset);\n  }\n\n  if (navStart && differenceInCalendarMonths(initialMonth, navStart) < 0) {\n    initialMonth = navStart;\n  }\n\n  return startOfMonth(initialMonth);\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport { CalendarWeek, CalendarDay, CalendarMonth } from \"../classes/index.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Returns the months to display in the calendar.\n *\n * This function generates `CalendarMonth` objects for each month to be\n * displayed, including their weeks and days, based on the provided display\n * months and dates.\n *\n * @param displayMonths The months (as dates) to display in the calendar.\n * @param dates The dates to display in the calendar.\n * @param props Options from the DayPicker props context.\n * @param dateLib The date library to use for date manipulation.\n * @returns An array of `CalendarMonth` objects representing the months to\n *   display.\n */\nexport function getMonths(\n  displayMonths: Date[],\n  dates: Date[],\n  props: Pick<\n    DayPickerProps,\n    \"broadcastCalendar\" | \"fixedWeeks\" | \"ISOWeek\" | \"reverseMonths\"\n  >,\n  dateLib: DateLib\n): CalendarMonth[] {\n  const {\n    addDays,\n    endOfBroadcastWeek,\n    endOfISOWeek,\n    endOfMonth,\n    endOfWeek,\n    getISOWeek,\n    getWeek,\n    startOfBroadcastWeek,\n    startOfISOWeek,\n    startOfWeek\n  } = dateLib;\n\n  const dayPickerMonths = displayMonths.reduce<CalendarMonth[]>(\n    (months, month) => {\n      const firstDateOfFirstWeek = props.broadcastCalendar\n        ? startOfBroadcastWeek(month, dateLib)\n        : props.ISOWeek\n          ? startOfISOWeek(month)\n          : startOfWeek(month);\n\n      const lastDateOfLastWeek = props.broadcastCalendar\n        ? endOfBroadcastWeek(month)\n        : props.ISOWeek\n          ? endOfISOWeek(endOfMonth(month))\n          : endOfWeek(endOfMonth(month));\n\n      /** The dates to display in the month. */\n      const monthDates = dates.filter((date) => {\n        return date >= firstDateOfFirstWeek && date <= lastDateOfLastWeek;\n      });\n\n      const nrOfDaysWithFixedWeeks = props.broadcastCalendar ? 35 : 42;\n\n      if (props.fixedWeeks && monthDates.length < nrOfDaysWithFixedWeeks) {\n        const extraDates = dates.filter((date) => {\n          const daysToAdd = nrOfDaysWithFixedWeeks - monthDates.length;\n          return (\n            date > lastDateOfLastWeek &&\n            date <= addDays(lastDateOfLastWeek, daysToAdd)\n          );\n        });\n        monthDates.push(...extraDates);\n      }\n\n      const weeks: CalendarWeek[] = monthDates.reduce<CalendarWeek[]>(\n        (weeks, date) => {\n          const weekNumber = props.ISOWeek ? getISOWeek(date) : getWeek(date);\n          const week = weeks.find((week) => week.weekNumber === weekNumber);\n\n          const day = new CalendarDay(date, month, dateLib);\n          if (!week) {\n            weeks.push(new CalendarWeek(weekNumber, [day]));\n          } else {\n            week.days.push(day);\n          }\n          return weeks;\n        },\n        []\n      );\n\n      const dayPickerMonth = new CalendarMonth(month, weeks);\n      months.push(dayPickerMonth);\n      return months;\n    },\n    []\n  );\n\n  if (!props.reverseMonths) {\n    return dayPickerMonths;\n  } else {\n    return dayPickerMonths.reverse();\n  }\n}\n", "import { type DateLib } from \"../classes/DateLib.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Returns the start and end months for calendar navigation.\n *\n * @param props The DayPicker props, including navigation and layout options.\n * @param dateLib The date library to use for date manipulation.\n * @returns A tuple containing the start and end months for navigation.\n */\nexport function getNavMonths(\n  props: Pick<\n    DayPickerProps,\n    | \"captionLayout\"\n    | \"endMonth\"\n    | \"startMonth\"\n    | \"today\"\n    | \"timeZone\"\n    // Deprecated:\n    | \"fromMonth\"\n    | \"fromYear\"\n    | \"toMonth\"\n    | \"toYear\"\n  >,\n  dateLib: DateLib\n): [start: Date | undefined, end: Date | undefined] {\n  let { startMonth, endMonth } = props;\n\n  const {\n    startOfYear,\n    startOfDay,\n    startOfMonth,\n    endOfMonth,\n    addYears,\n    endOfYear,\n    newDate,\n    today\n  } = dateLib;\n\n  // Handle deprecated code\n  const { fromYear, toYear, fromMonth, toMonth } = props;\n  if (!startMonth && fromMonth) {\n    startMonth = fromMonth;\n  }\n  if (!startMonth && fromYear) {\n    startMonth = dateLib.newDate(fromYear, 0, 1);\n  }\n  if (!endMonth && toMonth) {\n    endMonth = toMonth;\n  }\n  if (!endMonth && toYear) {\n    endMonth = newDate(toYear, 11, 31);\n  }\n\n  const hasYearDropdown =\n    props.captionLayout === \"dropdown\" ||\n    props.captionLayout === \"dropdown-years\";\n  if (startMonth) {\n    startMonth = startOfMonth(startMonth);\n  } else if (fromYear) {\n    startMonth = newDate(fromYear, 0, 1);\n  } else if (!startMonth && hasYearDropdown) {\n    startMonth = startOfYear(addYears(props.today ?? today(), -100));\n  }\n  if (endMonth) {\n    endMonth = endOfMonth(endMonth);\n  } else if (toYear) {\n    endMonth = newDate(toYear, 11, 31);\n  } else if (!endMonth && hasYearDropdown) {\n    endMonth = endOfYear(props.today ?? today());\n  }\n  return [\n    startMonth ? startOfDay(startMonth) : startMonth,\n    endMonth ? startOfDay(endMonth) : endMonth\n  ];\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Returns the next month the user can navigate to, based on the given options.\n *\n * The next month is not always the next calendar month:\n *\n * - If it is after the `calendarEndMonth`, it returns `undefined`.\n * - If paged navigation is enabled, it skips forward by the number of displayed\n *   months.\n *\n * @param firstDisplayedMonth The first month currently displayed in the\n *   calendar.\n * @param calendarEndMonth The latest month the user can navigate to.\n * @param options Navigation options, including `numberOfMonths` and\n *   `pagedNavigation`.\n * @param dateLib The date library to use for date manipulation.\n * @returns The next month, or `undefined` if navigation is not possible.\n */\nexport function getNextMonth(\n  firstDisplayedMonth: Date,\n  calendarEndMonth: Date | undefined,\n  options: Pick<\n    DayPickerProps,\n    \"numberOfMonths\" | \"pagedNavigation\" | \"disableNavigation\"\n  >,\n  dateLib: DateLib\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { pagedNavigation, numberOfMonths = 1 } = options;\n  const { startOfMonth, addMonths, differenceInCalendarMonths } = dateLib;\n  const offset = pagedNavigation ? numberOfMonths : 1;\n  const month = startOfMonth(firstDisplayedMonth);\n\n  if (!calendarEndMonth) {\n    return addMonths(month, offset);\n  }\n\n  const monthsDiff = differenceInCalendarMonths(\n    calendarEndMonth,\n    firstDisplayedMonth\n  );\n\n  if (monthsDiff < numberOfMonths) {\n    return undefined;\n  }\n\n  return addMonths(month, offset);\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport type { DayPickerProps } from \"../types/index.js\";\n\n/**\n * Returns the previous month the user can navigate to, based on the given\n * options.\n *\n * The previous month is not always the previous calendar month:\n *\n * - If it is before the `calendarStartMonth`, it returns `undefined`.\n * - If paged navigation is enabled, it skips back by the number of displayed\n *   months.\n *\n * @param firstDisplayedMonth The first month currently displayed in the\n *   calendar.\n * @param calendarStartMonth The earliest month the user can navigate to.\n * @param options Navigation options, including `numberOfMonths` and\n *   `pagedNavigation`.\n * @param dateLib The date library to use for date manipulation.\n * @returns The previous month, or `undefined` if navigation is not possible.\n */\nexport function getPreviousMonth(\n  firstDisplayedMonth: Date,\n  calendarStartMonth: Date | undefined,\n  options: Pick<\n    DayPickerProps,\n    \"numberOfMonths\" | \"pagedNavigation\" | \"disableNavigation\"\n  >,\n  dateLib: DateLib\n): Date | undefined {\n  if (options.disableNavigation) {\n    return undefined;\n  }\n  const { pagedNavigation, numberOfMonths } = options;\n  const { startOfMonth, addMonths, differenceInCalendarMonths } = dateLib;\n  const offset = pagedNavigation ? (numberOfMonths ?? 1) : 1;\n  const month = startOfMonth(firstDisplayedMonth);\n  if (!calendarStartMonth) {\n    return addMonths(month, -offset);\n  }\n  const monthsDiff = differenceInCalendarMonths(month, calendarStartMonth);\n\n  if (monthsDiff <= 0) {\n    return undefined;\n  }\n\n  return addMonths(month, -offset);\n}\n", "import type { CalendarMonth, CalendarWeek } from \"../classes/index.js\";\n\n/**\n * Returns an array of calendar weeks from an array of calendar months.\n *\n * @param months The array of calendar months.\n * @returns An array of calendar weeks.\n */\nexport function getWeeks(months: CalendarMonth[]) {\n  const initialWeeks: CalendarWeek[] = [];\n  return months.reduce((weeks, month) => {\n    return [...weeks, ...month.weeks];\n  }, initialWeeks);\n}\n", "import { useState } from \"react\";\n\nexport type DispatchStateAction<T> = React.Dispatch<React.SetStateAction<T>>;\n\n/**\n * A custom hook for managing both controlled and uncontrolled component states.\n *\n * This hook allows a component to support both controlled and uncontrolled\n * states by determining whether the `controlledValue` is provided. If it is\n * undefined, the hook falls back to using the internal state.\n *\n * @example\n *   // Uncontrolled usage\n *   const [value, setValue] = useControlledValue(0, undefined);\n *\n *   // Controlled usage\n *   const [value, setValue] = useControlledValue(0, props.value);\n *\n * @template T - The type of the value.\n * @param defaultValue The initial value for the uncontrolled state.\n * @param controlledValue The value for the controlled state. If undefined, the\n *   component will use the uncontrolled state.\n * @returns A tuple where the first element is the current value (either\n *   controlled or uncontrolled) and the second element is a setter function to\n *   update the value.\n */\nexport function useControlledValue<T>(\n  defaultValue: T,\n  controlledValue: T | undefined\n): [T, DispatchStateAction<T>] {\n  const [uncontrolledValue, setValue] = useState(defaultValue);\n\n  const value =\n    controlledValue === undefined ? uncontrolledValue : controlledValue;\n\n  return [value, setValue] as [T, DispatchStateAction<T>];\n}\n", "import { useState } from \"react\";\n\nimport type { CalendarDay, DateLib } from \"./classes/index.js\";\nimport { calculateFocusTarget } from \"./helpers/calculateFocusTarget.js\";\nimport { getNextFocus } from \"./helpers/getNextFocus.js\";\nimport type {\n  MoveFocusBy,\n  MoveFocusDir,\n  DayPickerProps,\n  Modifiers\n} from \"./types/index.js\";\nimport { Calendar } from \"./useCalendar.js\";\n\nexport type UseFocus = {\n  /** The date that is currently focused. */\n  focused: CalendarDay | undefined;\n\n  /** Check if the given day is the focus target when entering the calendar. */\n  isFocusTarget: (day: CalendarDay) => boolean;\n\n  /** Focus the given day. */\n  setFocused: (day: CalendarDay | undefined) => void;\n\n  /** Blur the focused day. */\n  blur: () => void;\n\n  /** Move the current focus to the next day according to the given direction. */\n  moveFocus: (moveBy: MoveFocusBy, moveDir: MoveFocusDir) => void;\n};\n\n/**\n * Manages focus behavior for the DayPicker component, including setting,\n * moving, and blurring focus on calendar days.\n *\n * @template T - The type of DayPicker props.\n * @param props - The DayPicker props.\n * @param calendar - The calendar object containing the displayed days and\n *   months.\n * @param getModifiers - A function to retrieve modifiers for a given day.\n * @param isSelected - A function to check if a date is selected.\n * @param dateLib - The date utility library instance.\n * @returns An object containing focus-related methods and the currently focused\n *   day.\n */\nexport function useFocus<T extends DayPickerProps>(\n  props: T,\n  calendar: Calendar,\n  getModifiers: (day: CalendarDay) => Modifiers,\n  isSelected: (date: Date) => boolean,\n  dateLib: DateLib\n): UseFocus {\n  const { autoFocus } = props;\n  const [lastFocused, setLastFocused] = useState<CalendarDay | undefined>();\n\n  const focusTarget = calculateFocusTarget(\n    calendar.days,\n    getModifiers,\n    isSelected || (() => false),\n    lastFocused\n  );\n  const [focusedDay, setFocused] = useState<CalendarDay | undefined>(\n    autoFocus ? focusTarget : undefined\n  );\n\n  const blur = () => {\n    setLastFocused(focusedDay);\n    setFocused(undefined);\n  };\n\n  const moveFocus = (moveBy: MoveFocusBy, moveDir: MoveFocusDir) => {\n    if (!focusedDay) return;\n    const nextFocus = getNextFocus(\n      moveBy,\n      moveDir,\n      focusedDay,\n      calendar.navStart,\n      calendar.navEnd,\n      props,\n      dateLib\n    );\n    if (!nextFocus) return;\n\n    calendar.goToDay(nextFocus);\n    setFocused(nextFocus);\n  };\n\n  const isFocusTarget = (day: CalendarDay) => {\n    return Boolean(focusTarget?.isEqualTo(day));\n  };\n\n  const useFocus: UseFocus = {\n    isFocusTarget,\n    setFocused,\n    focused: focusedDay,\n    blur,\n    moveFocus\n  };\n\n  return useFocus;\n}\n", "import { DayFlag } from \"../UI.js\";\nimport type { CalendarDay } from \"../classes/index.js\";\nimport type { Modifiers } from \"../types/index.js\";\n\nenum FocusTargetPriority {\n  Today = 0,\n  Selected,\n  LastFocused,\n  FocusedModifier\n}\n\n/**\n * Determines if a day is focusable based on its modifiers.\n *\n * A day is considered focusable if it is not disabled, hidden, or outside the\n * displayed month.\n *\n * @param modifiers The modifiers applied to the day.\n * @returns `true` if the day is focusable, otherwise `false`.\n */\nfunction isFocusableDay(modifiers: Modifiers) {\n  return (\n    !modifiers[DayFlag.disabled] &&\n    !modifiers[DayFlag.hidden] &&\n    !modifiers[DayFlag.outside]\n  );\n}\n\n/**\n * Calculates the focus target day based on priority.\n *\n * This function determines the day that should receive focus in the calendar,\n * prioritizing days with specific modifiers (e.g., \"focused\", \"today\") or\n * selection states.\n *\n * @param days The array of `CalendarDay` objects to evaluate.\n * @param getModifiers A function to retrieve the modifiers for a given day.\n * @param isSelected A function to determine if a day is selected.\n * @param lastFocused The last focused day, if any.\n * @returns The `CalendarDay` that should receive focus, or `undefined` if no\n *   focusable day is found.\n */\nexport function calculateFocusTarget(\n  days: CalendarDay[],\n  getModifiers: (day: CalendarDay) => Modifiers,\n  isSelected: (date: Date) => boolean,\n  lastFocused: CalendarDay | undefined\n): CalendarDay | undefined {\n  let focusTarget: CalendarDay | undefined;\n\n  let foundFocusTargetPriority: FocusTargetPriority | -1 = -1;\n  for (const day of days) {\n    const modifiers = getModifiers(day);\n\n    if (isFocusableDay(modifiers)) {\n      if (\n        modifiers[DayFlag.focused] &&\n        foundFocusTargetPriority < FocusTargetPriority.FocusedModifier\n      ) {\n        focusTarget = day;\n        foundFocusTargetPriority = FocusTargetPriority.FocusedModifier;\n      } else if (\n        lastFocused?.isEqualTo(day) &&\n        foundFocusTargetPriority < FocusTargetPriority.LastFocused\n      ) {\n        focusTarget = day;\n        foundFocusTargetPriority = FocusTargetPriority.LastFocused;\n      } else if (\n        isSelected(day.date) &&\n        foundFocusTargetPriority < FocusTargetPriority.Selected\n      ) {\n        focusTarget = day;\n        foundFocusTargetPriority = FocusTargetPriority.Selected;\n      } else if (\n        modifiers[DayFlag.today] &&\n        foundFocusTargetPriority < FocusTargetPriority.Today\n      ) {\n        focusTarget = day;\n        foundFocusTargetPriority = FocusTargetPriority.Today;\n      }\n    }\n  }\n\n  if (!focusTarget) {\n    // Return the first day that is focusable\n    focusTarget = days.find((day) => isFocusableDay(getModifiers(day)));\n  }\n  return focusTarget;\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport type {\n  DayPickerProps,\n  MoveFocusBy,\n  MoveFocusDir\n} from \"../types/index.js\";\n\n/**\n * Calculates the next date that should be focused in the calendar.\n *\n * This function determines the next focusable date based on the movement\n * direction, constraints, and calendar configuration.\n *\n * @param moveBy The unit of movement (e.g., \"day\", \"week\").\n * @param moveDir The direction of movement (\"before\" or \"after\").\n * @param refDate The reference date from which to calculate the next focusable\n *   date.\n * @param navStart The earliest date the user can navigate to.\n * @param navEnd The latest date the user can navigate to.\n * @param props The DayPicker props, including calendar configuration options.\n * @param dateLib The date library to use for date manipulation.\n * @returns The next focusable date.\n */\nexport function getFocusableDate(\n  moveBy: MoveFocusBy,\n  moveDir: MoveFocusDir,\n  refDate: Date,\n  navStart: Date | undefined,\n  navEnd: Date | undefined,\n  props: Pick<DayPickerProps, \"ISOWeek\" | \"broadcastCalendar\">,\n  dateLib: DateLib\n): Date {\n  const { ISOWeek, broadcastCalendar } = props;\n  const {\n    addDays,\n    addMonths,\n    addWeeks,\n    addYears,\n    endOfBroadcastWeek,\n    endOfISOWeek,\n    endOfWeek,\n    max,\n    min,\n    startOfBroadcastWeek,\n    startOfISOWeek,\n    startOfWeek\n  } = dateLib;\n  const moveFns = {\n    day: addDays,\n    week: addWeeks,\n    month: addMonths,\n    year: addYears,\n    startOfWeek: (date: Date) =>\n      broadcastCalendar\n        ? startOfBroadcastWeek(date, dateLib)\n        : ISOWeek\n          ? startOfISOWeek(date)\n          : startOfWeek(date),\n    endOfWeek: (date: Date) =>\n      broadcastCalendar\n        ? endOfBroadcastWeek(date)\n        : ISOWeek\n          ? endOfISOWeek(date)\n          : endOfWeek(date)\n  };\n\n  let focusableDate = moveFns[moveBy](refDate, moveDir === \"after\" ? 1 : -1);\n  if (moveDir === \"before\" && navStart) {\n    focusableDate = max([navStart, focusableDate]);\n  } else if (moveDir === \"after\" && navEnd) {\n    focusableDate = min([navEnd, focusableDate]);\n  }\n  return focusableDate;\n}\n", "import type { DateLib } from \"../classes/DateLib.js\";\nimport { CalendarDay } from \"../classes/index.js\";\nimport type {\n  DayPickerProps,\n  MoveFocusBy,\n  MoveFocusDir\n} from \"../types/index.js\";\nimport { dateMatchModifiers } from \"../utils/dateMatchModifiers.js\";\n\nimport { getFocusableDate } from \"./getFocusableDate.js\";\n\n/**\n * Determines the next focusable day in the calendar.\n *\n * This function recursively calculates the next focusable day based on the\n * movement direction and modifiers applied to the days.\n *\n * @param moveBy The unit of movement (e.g., \"day\", \"week\").\n * @param moveDir The direction of movement (\"before\" or \"after\").\n * @param refDay The currently focused day.\n * @param calendarStartMonth The earliest month the user can navigate to.\n * @param calendarEndMonth The latest month the user can navigate to.\n * @param props The DayPicker props, including modifiers and configuration\n *   options.\n * @param dateLib The date library to use for date manipulation.\n * @param attempt The current recursion attempt (used to limit recursion depth).\n * @returns The next focusable day, or `undefined` if no focusable day is found.\n */\nexport function getNextFocus(\n  moveBy: MoveFocusBy,\n  moveDir: MoveFocusDir,\n  refDay: CalendarDay,\n  calendarStartMonth: Date | undefined,\n  calendarEndMonth: Date | undefined,\n  props: Pick<\n    DayPickerProps,\n    \"disabled\" | \"hidden\" | \"modifiers\" | \"ISOWeek\" | \"timeZone\"\n  >,\n  dateLib: DateLib,\n  attempt: number = 0\n): CalendarDay | undefined {\n  if (attempt > 365) {\n    // Limit the recursion to 365 attempts\n    return undefined;\n  }\n\n  const focusableDate = getFocusableDate(\n    moveBy,\n    moveDir,\n    refDay.date,\n    calendarStartMonth,\n    calendarEndMonth,\n    props,\n    dateLib\n  );\n\n  const isDisabled = Boolean(\n    props.disabled && dateMatchModifiers(focusableDate, props.disabled, dateLib)\n  );\n\n  const isHidden = Boolean(\n    props.hidden && dateMatchModifiers(focusableDate, props.hidden, dateLib)\n  );\n\n  const targetMonth = focusableDate;\n  const focusDay = new CalendarDay(focusableDate, targetMonth, dateLib);\n\n  if (!isDisabled && !isHidden) {\n    return focusDay;\n  }\n\n  // Recursively attempt to find the next focusable date\n  return getNextFocus(\n    moveBy,\n    moveDir,\n    focusDay,\n    calendarStartMonth,\n    calendarEndMonth,\n    props,\n    dateLib,\n    attempt + 1\n  );\n}\n", "import React from \"react\";\n\nimport type { DateLib } from \"../classes/DateLib.js\";\nimport { useControlledValue } from \"../helpers/useControlledValue.js\";\nimport type {\n  DayPickerProps,\n  Modifiers,\n  PropsMulti,\n  Selection\n} from \"../types/index.js\";\n\n/**\n * Hook to manage multiple-date selection in the DayPicker component.\n *\n * @template T - The type of DayPicker props.\n * @param props - The DayPicker props.\n * @param dateLib - The date utility library instance.\n * @returns An object containing the selected dates, a function to select dates,\n *   and a function to check if a date is selected.\n */\nexport function useMulti<T extends DayPickerProps>(\n  props: T,\n  dateLib: DateLib\n): Selection<T> {\n  const {\n    selected: initiallySelected,\n    required,\n    onSelect\n  } = props as PropsMulti;\n\n  const [internallySelected, setSelected] = useControlledValue(\n    initiallySelected,\n    onSelect ? initiallySelected : undefined\n  );\n\n  const selected = !onSelect ? internallySelected : initiallySelected;\n\n  const { isSameDay } = dateLib;\n\n  const isSelected = (date: Date) => {\n    return selected?.some((d) => isSameDay(d, date)) ?? false;\n  };\n\n  const { min, max } = props as PropsMulti;\n\n  const select = (\n    triggerDate: Date,\n    modifiers: Modifiers,\n    e: React.MouseEvent | React.KeyboardEvent\n  ) => {\n    let newDates: Date[] | undefined = [...(selected ?? [])];\n    if (isSelected(triggerDate)) {\n      if (selected?.length === min) {\n        // Min value reached, do nothing\n        return;\n      }\n      if (required && selected?.length === 1) {\n        // Required value already selected do nothing\n        return;\n      }\n      newDates = selected?.filter((d) => !isSameDay(d, triggerDate));\n    } else {\n      if (selected?.length === max) {\n        // Max value reached, reset the selection to date\n        newDates = [triggerDate];\n      } else {\n        // Add the date to the selection\n        newDates = [...newDates, triggerDate];\n      }\n    }\n    if (!onSelect) {\n      setSelected(newDates);\n    }\n    onSelect?.(newDates, triggerDate, modifiers, e);\n    return newDates;\n  };\n\n  return {\n    selected,\n    select,\n    isSelected\n  } as Selection<T>;\n}\n", "import { defaultDateLib, type DateLib } from \"../classes/DateLib.js\";\nimport type { DateRange } from \"../types/index.js\";\n\n/**\n * Adds a date to an existing range, considering constraints like minimum and\n * maximum range size.\n *\n * @param date - The date to add to the range.\n * @param initialRange - The initial range to which the date will be added.\n * @param min - The minimum number of days in the range.\n * @param max - The maximum number of days in the range.\n * @param required - Whether the range must always include at least one date.\n * @param dateLib - The date utility library instance.\n * @returns The updated date range, or `undefined` if the range is cleared.\n * @group Utilities\n */\nexport function addToRange(\n  date: Date,\n  initialRange: DateRange | undefined,\n  min = 0,\n  max = 0,\n  required = false,\n  dateLib: DateLib = defaultDateLib\n): DateRange | undefined {\n  const { from, to } = initialRange || {};\n  const { isSameDay, isAfter, isBefore } = dateLib;\n\n  let range: DateRange | undefined;\n\n  if (!from && !to) {\n    // the range is empty, add the date\n    range = { from: date, to: min > 0 ? undefined : date };\n  } else if (from && !to) {\n    // adding date to an incomplete range\n    if (isSameDay(from, date)) {\n      // adding a date equal to the start of the range\n      if (required) {\n        range = { from, to: undefined };\n      } else {\n        range = undefined;\n      }\n    } else if (isBefore(date, from)) {\n      // adding a date before the start of the range\n      range = { from: date, to: from };\n    } else {\n      // adding a date after the start of the range\n      range = { from, to: date };\n    }\n  } else if (from && to) {\n    // adding date to a complete range\n    if (isSameDay(from, date) && isSameDay(to, date)) {\n      // adding a date that is equal to both start and end of the range\n      if (required) {\n        range = { from, to };\n      } else {\n        range = undefined;\n      }\n    } else if (isSameDay(from, date)) {\n      // adding a date equal to the the start of the range\n      range = { from, to: min > 0 ? undefined : date };\n    } else if (isSameDay(to, date)) {\n      // adding a dare equal to the end of the range\n      range = { from: date, to: min > 0 ? undefined : date };\n    } else if (isBefore(date, from)) {\n      // adding a date before the start of the range\n      range = { from: date, to: to };\n    } else if (isAfter(date, from)) {\n      // adding a date after the start of the range\n      range = { from, to: date };\n    } else if (isAfter(date, to)) {\n      // adding a date after the end of the range\n      range = { from, to: date };\n    } else {\n      throw new Error(\"Invalid range\");\n    }\n  }\n\n  // check for min / max\n  if (range?.from && range?.to) {\n    const diff = dateLib.differenceInCalendarDays(range.to, range.from);\n    if (max > 0 && diff > max) {\n      range = { from: date, to: undefined };\n    } else if (min > 1 && diff < min) {\n      range = { from: date, to: undefined };\n    }\n  }\n\n  return range;\n}\n", "import { defaultDateLib, type DateLib } from \"../classes/DateLib.js\";\n\n/**\n * Checks if a date range contains one or more specified days of the week.\n *\n * @since 9.2.2\n * @param range - The date range to check.\n * @param dayOfWeek - The day(s) of the week to check for (`0-6`, where `0` is\n *   Sunday).\n * @param dateLib - The date utility library instance.\n * @returns `true` if the range contains the specified day(s) of the week,\n *   otherwise `false`.\n * @group Utilities\n */\nexport function rangeContainsDayOfWeek(\n  range: { from: Date; to: Date },\n  dayOfWeek: number | number[],\n  dateLib: DateLib = defaultDateLib\n) {\n  const dayOfWeekArr = !Array.isArray(dayOfWeek) ? [dayOfWeek] : dayOfWeek;\n  let date = range.from;\n  const totalDays = dateLib.differenceInCalendarDays(range.to, range.from);\n\n  // iterate at maximum one week or the total days if the range is shorter than one week\n  const totalDaysLimit = Math.min(totalDays, 6);\n  for (let i = 0; i <= totalDaysLimit; i++) {\n    if (dayOfWeekArr.includes(date.getDay())) {\n      return true;\n    }\n    date = dateLib.addDays(date, 1);\n  }\n  return false;\n}\n", "import { defaultDateLib } from \"../classes/index.js\";\n\nimport { rangeIncludesDate } from \"./rangeIncludesDate.js\";\n\n/**\n * Determines if two date ranges overlap.\n *\n * @since 9.2.2\n * @param rangeLeft - The first date range.\n * @param rangeRight - The second date range.\n * @param dateLib - The date utility library instance.\n * @returns `true` if the ranges overlap, otherwise `false`.\n * @group Utilities\n */\nexport function rangeOverlaps(\n  rangeLeft: { from: Date; to: Date },\n  rangeRight: { from: Date; to: Date },\n  dateLib = defaultDateLib\n): boolean {\n  return (\n    rangeIncludesDate(rangeLeft, rangeRight.from, false, dateLib) ||\n    rangeIncludesDate(rangeLeft, rangeRight.to, false, dateLib) ||\n    rangeIncludesDate(rangeRight, rangeLeft.from, false, dateLib) ||\n    rangeIncludesDate(rangeRight, rangeLeft.to, false, dateLib)\n  );\n}\n", "import { defaultDateLib, type DateLib } from \"../classes/DateLib.js\";\nimport type { Matcher } from \"../types/index.js\";\n\nimport { dateMatchModifiers } from \"./dateMatchModifiers.js\";\nimport { rangeContainsDayOfWeek } from \"./rangeContainsDayOfWeek.js\";\nimport { rangeIncludesDate } from \"./rangeIncludesDate.js\";\nimport { rangeOverlaps } from \"./rangeOverlaps.js\";\nimport {\n  isDateAfterType,\n  isDateBeforeType,\n  isDateInterval,\n  isDateRange,\n  isDatesArray,\n  isDayOfWeekType\n} from \"./typeguards.js\";\n\n/**\n * Checks if a date range contains dates that match the given modifiers.\n *\n * @since 9.2.2\n * @param range - The date range to check.\n * @param modifiers - The modifiers to match against.\n * @param dateLib - The date utility library instance.\n * @returns `true` if the range contains matching dates, otherwise `false`.\n * @group Utilities\n */\nexport function rangeContainsModifiers(\n  range: { from: Date; to: Date },\n  modifiers: Matcher | Matcher[],\n  dateLib: DateLib = defaultDateLib\n): boolean {\n  const matchers = Array.isArray(modifiers) ? modifiers : [modifiers];\n\n  // Defer function matchers evaluation as they are the least performant.\n  const nonFunctionMatchers = matchers.filter(\n    (matcher) => typeof matcher !== \"function\"\n  );\n\n  const nonFunctionMatchersResult = nonFunctionMatchers.some((matcher) => {\n    if (typeof matcher === \"boolean\") return matcher;\n\n    if (dateLib.isDate(matcher)) {\n      return rangeIncludesDate(range, matcher, false, dateLib);\n    }\n\n    if (isDatesArray(matcher, dateLib)) {\n      return matcher.some((date) =>\n        rangeIncludesDate(range, date, false, dateLib)\n      );\n    }\n\n    if (isDateRange(matcher)) {\n      if (matcher.from && matcher.to) {\n        return rangeOverlaps(\n          range,\n          { from: matcher.from, to: matcher.to },\n          dateLib\n        );\n      }\n      return false;\n    }\n\n    if (isDayOfWeekType(matcher)) {\n      return rangeContainsDayOfWeek(range, matcher.dayOfWeek, dateLib);\n    }\n\n    if (isDateInterval(matcher)) {\n      const isClosedInterval = dateLib.isAfter(matcher.before, matcher.after);\n      if (isClosedInterval) {\n        return rangeOverlaps(\n          range,\n          {\n            from: dateLib.addDays(matcher.after, 1),\n            to: dateLib.addDays(matcher.before, -1)\n          },\n          dateLib\n        );\n      }\n      return (\n        dateMatchModifiers(range.from, matcher, dateLib) ||\n        dateMatchModifiers(range.to, matcher, dateLib)\n      );\n    }\n\n    if (isDateAfterType(matcher) || isDateBeforeType(matcher)) {\n      return (\n        dateMatchModifiers(range.from, matcher, dateLib) ||\n        dateMatchModifiers(range.to, matcher, dateLib)\n      );\n    }\n\n    return false;\n  });\n\n  if (nonFunctionMatchersResult) {\n    return true;\n  }\n\n  const functionMatchers = matchers.filter(\n    (matcher) => typeof matcher === \"function\"\n  );\n\n  if (functionMatchers.length) {\n    let date = range.from;\n    const totalDays = dateLib.differenceInCalendarDays(range.to, range.from);\n\n    for (let i = 0; i <= totalDays; i++) {\n      if (functionMatchers.some((matcher) => matcher(date))) {\n        return true;\n      }\n      date = dateLib.addDays(date, 1);\n    }\n  }\n\n  return false;\n}\n", "import React from \"react\";\n\nimport type { DateLib } from \"../classes/DateLib.js\";\nimport { useControlledValue } from \"../helpers/useControlledValue.js\";\nimport type {\n  DayPickerProps,\n  Modifiers,\n  PropsRange,\n  Selection\n} from \"../types/index.js\";\nimport { addToRange, rangeContainsModifiers } from \"../utils/index.js\";\nimport { rangeIncludesDate } from \"../utils/rangeIncludesDate.js\";\n\n/**\n * Hook to manage range selection in the DayPicker component.\n *\n * @template T - The type of DayPicker props.\n * @param props - The DayPicker props.\n * @param dateLib - The date utility library instance.\n * @returns An object containing the selected range, a function to select a\n *   range, and a function to check if a date is within the range.\n */\nexport function useRange<T extends DayPickerProps>(\n  props: T,\n  dateLib: DateLib\n): Selection<T> {\n  const {\n    disabled,\n    excludeDisabled,\n    selected: initiallySelected,\n    required,\n    onSelect\n  } = props as PropsRange;\n\n  const [internallySelected, setSelected] = useControlledValue(\n    initiallySelected,\n    onSelect ? initiallySelected : undefined\n  );\n\n  const selected = !onSelect ? internallySelected : initiallySelected;\n\n  const isSelected = (date: Date) =>\n    selected && rangeIncludesDate(selected, date, false, dateLib);\n\n  const select = (\n    triggerDate: Date,\n    modifiers: Modifiers,\n    e: React.MouseEvent | React.KeyboardEvent\n  ) => {\n    const { min, max } = props as PropsRange;\n    const newRange = triggerDate\n      ? addToRange(triggerDate, selected, min, max, required, dateLib)\n      : undefined;\n\n    if (excludeDisabled && disabled && newRange?.from && newRange.to) {\n      if (\n        rangeContainsModifiers(\n          { from: newRange.from, to: newRange.to },\n          disabled,\n          dateLib\n        )\n      ) {\n        // if a disabled days is found, the range is reset\n        newRange.from = triggerDate;\n        newRange.to = undefined;\n      }\n    }\n\n    if (!onSelect) {\n      setSelected(newRange);\n    }\n    onSelect?.(newRange, triggerDate, modifiers, e);\n\n    return newRange;\n  };\n\n  return {\n    selected,\n    select,\n    isSelected\n  } as Selection<T>;\n}\n", "import React from \"react\";\n\nimport type { DateLib } from \"../classes/DateLib.js\";\nimport { useControlledValue } from \"../helpers/useControlledValue.js\";\nimport type {\n  DayPickerProps,\n  Modifiers,\n  PropsSingle,\n  SelectHandler,\n  SelectedValue,\n  Selection\n} from \"../types/index.js\";\n\nexport type UseSingle<T extends DayPickerProps> = {\n  select: SelectHandler<T>;\n  isSelected: (date: Date) => boolean;\n  selected: SelectedValue<T>;\n};\n\n/**\n * Hook to manage single-date selection in the DayPicker component.\n *\n * @template T - The type of DayPicker props.\n * @param props - The DayPicker props.\n * @param dateLib - The date utility library instance.\n * @returns An object containing the selected date, a function to select a date,\n *   and a function to check if a date is selected.\n */\nexport function useSingle<T extends DayPickerProps>(\n  props: DayPickerProps,\n  dateLib: DateLib\n): Selection<T> {\n  const {\n    selected: initiallySelected,\n    required,\n    onSelect\n  } = props as PropsSingle;\n\n  const [internallySelected, setSelected] = useControlledValue(\n    initiallySelected,\n    onSelect ? initiallySelected : undefined\n  );\n\n  const selected = !onSelect ? internallySelected : initiallySelected;\n\n  const { isSameDay } = dateLib;\n\n  const isSelected = (compareDate: Date) => {\n    return selected ? isSameDay(selected, compareDate) : false;\n  };\n\n  const select = (\n    triggerDate: Date,\n    modifiers: Modifiers,\n    e: React.MouseEvent | React.KeyboardEvent\n  ) => {\n    let newDate: Date | undefined = triggerDate;\n    if (!required && selected && selected && isSameDay(triggerDate, selected)) {\n      // If the date is the same, clear the selection.\n      newDate = undefined;\n    }\n    if (!onSelect) {\n      setSelected(newDate);\n    }\n    if (required) {\n      onSelect?.(newDate as Date, triggerDate, modifiers, e);\n    } else {\n      onSelect?.(newDate, triggerDate, modifiers, e);\n    }\n    return newDate;\n  };\n\n  return {\n    selected,\n    select,\n    isSelected\n  } as Selection<T>;\n}\n", "import { type DateLib } from \"./classes/DateLib.js\";\nimport { useMulti } from \"./selection/useMulti.js\";\nimport { useRange } from \"./selection/useRange.js\";\nimport { useSingle } from \"./selection/useSingle.js\";\nimport type { DayPickerProps } from \"./types/index.js\";\nimport { Selection } from \"./types/selection.js\";\n\n/**\n * Determines the appropriate selection hook to use based on the selection mode\n * and returns the corresponding selection object.\n *\n * @template T - The type of DayPicker props.\n * @param props - The DayPicker props.\n * @param dateLib - The date utility library instance.\n * @returns The selection object for the specified mode, or `undefined` if no\n *   mode is set.\n */\nexport function useSelection<T extends DayPickerProps>(\n  props: T,\n  dateLib: DateLib\n): Selection<T> | undefined {\n  const single = useSingle(props, dateLib);\n  const multi = useMulti(props, dateLib);\n  const range = useRange(props, dateLib);\n\n  switch (props.mode) {\n    case \"single\":\n      return single;\n    case \"multiple\":\n      return multi;\n    case \"range\":\n      return range;\n    default:\n      return undefined;\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { DayFlag, SelectionState } from \"../UI.js\";\nimport {\n  MonthCaption,\n  type MonthCaptionProps\n} from \"../components/MonthCaption.js\";\nimport { Week, type WeekProps } from \"../components/Week.js\";\nimport {\n  labelDayButton,\n  labelNext,\n  labelWeekday,\n  labelWeekNumber\n} from \"../labels/index.js\";\nimport { useDayPicker } from \"../useDayPicker.js\";\n\nimport type { PropsMulti, PropsRange, PropsSingle } from \"./props.js\";\nimport type { Mode, DayEventHandler } from \"./shared.js\";\n\n/**\n * @ignore\n * @deprecated This type will be removed.\n */\nexport type RootProvider = any;\n\n/**\n * @ignore\n * @deprecated This type will be removed.\n */\nexport type RootProviderProps = any;\n\n/**\n * @ignore\n * @deprecated This component has been renamed. Use `MonthCaption` instead.\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport const Caption = MonthCaption;\n\n/**\n * @ignore\n * @deprecated This type has been renamed. Use `MonthCaptionProps` instead.\n */\nexport type CaptionProps = MonthCaptionProps;\n\n/**\n * @ignore\n * @deprecated This component has been removed.\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport type HeadRow = any;\n\n/**\n * @ignore\n * @deprecated This component has been renamed. Use `Week` instead.\n * @group Components\n * @see https://daypicker.dev/guides/custom-components\n */\nexport const Row = Week;\n\n/**\n * @ignore\n * @deprecated This type has been removed. Use `WeekProps` instead.\n */\nexport type RowProps = WeekProps;\n\n/**\n * @ignore\n * @deprecated This type has been renamed. Use `PropsSingle` instead.\n */\nexport type DayPickerSingleProps = PropsSingle;\n\n/**\n * @ignore\n * @deprecated This type has been renamed. Use `PropsMulti` instead.\n */\nexport type DayPickerMultipleProps = PropsMulti;\n\n/**\n * @ignore\n * @deprecated This type has been renamed. Use `PropsRange` instead.\n */\nexport type DayPickerRangeProps = PropsRange;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `NonNullable<unknown>` instead.\n */\nexport type DayPickerDefaultProps = NonNullable<unknown>;\n\n/**\n * @ignore\n * @deprecated This type has been renamed. Use `Mode` instead.\n */\nexport type DaySelectionMode = Mode;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `string` instead.\n */\nexport type Modifier = string;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use {@link DayFlag} or\n *   {@link SelectionState} instead.\n */\nexport type InternalModifier =\n  | DayFlag.disabled\n  | DayFlag.hidden\n  | DayFlag.focused\n  | SelectionState.range_end\n  | SelectionState.range_middle\n  | SelectionState.range_start\n  | SelectionState.selected;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `SelectHandler<{ mode: \"single\"\n *   }>` instead.\n */\nexport type SelectSingleEventHandler = PropsSingle[\"onSelect\"];\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `SelectHandler<{ mode: \"multiple\"\n *   }>` instead.\n */\nexport type SelectMultipleEventHandler = PropsMulti[\"onSelect\"];\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `SelectHandler<{ mode: \"range\" }>`\n *   instead.\n */\nexport type SelectRangeEventHandler = PropsRange[\"onSelect\"];\n\n/**\n * @ignore\n * @deprecated This type is not used anymore.\n */\nexport type DayPickerProviderProps = any;\n\n/**\n * @ignore\n * @deprecated This type has been moved to `useDayPicker`.\n * @group Hooks\n */\nexport const useNavigation = useDayPicker;\n\n/**\n * @ignore\n * @deprecated This hook has been removed. Use a custom `Day` component instead.\n * @group Hooks\n * @see https://daypicker.dev/guides/custom-components\n */\nexport type useDayRender = any;\n\n/**\n * @ignore\n * @deprecated This type is not used anymore.\n */\nexport type ContextProvidersProps = any;\n\n/**\n * @ignore\n * @deprecated Use `typeof labelDayButton` instead.\n */\nexport type DayLabel = typeof labelDayButton;\n\n/**\n * @ignore\n * @deprecated Use `typeof labelNext` or `typeof labelPrevious` instead.\n */\nexport type NavButtonLabel = typeof labelNext;\n\n/**\n * @ignore\n * @deprecated Use `typeof labelWeekday` instead.\n */\nexport type WeekdayLabel = typeof labelWeekday;\n\n/**\n * @ignore\n * @deprecated Use `typeof labelWeekNumber` instead.\n */\nexport type WeekNumberLabel = typeof labelWeekNumber;\n\n/**\n * @ignore\n * @deprecated Use {@link DayMouseEventHandler} instead.\n */\nexport type DayClickEventHandler = DayEventHandler<React.MouseEvent>;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use `DayEventHandler<React.FocusEvent\n *   | React.KeyboardEvent>` instead.\n */\nexport type DayFocusEventHandler = DayEventHandler<\n  React.FocusEvent | React.KeyboardEvent\n>;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use\n *   `DayEventHandler<React.KeyboardEvent>` instead.\n */\nexport type DayKeyboardEventHandler = DayEventHandler<React.KeyboardEvent>;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use\n *   `DayEventHandler<React.MouseEvent>` instead.\n */\nexport type DayMouseEventHandler = DayEventHandler<React.MouseEvent>;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use\n *   `DayEventHandler<React.PointerEvent>` instead.\n */\nexport type DayPointerEventHandler = DayEventHandler<React.PointerEvent>;\n\n/**\n * @ignore\n * @deprecated This type will be removed. Use\n *   `DayEventHandler<React.TouchEvent>` instead.\n */\nexport type DayTouchEventHandler = DayEventHandler<React.TouchEvent>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAoD;;;ACI7C,IAAM,sBAAsB,OAAO,IAAI,mBAAmB;;;ACJjE,IAAM,oBAAoB,CAAC;AAC3B,IAAM,cAAc,CAAC;AAed,SAAS,SAAS,UAAU,MAAM;AACvC,MAAI;AACF,UAAMC,UAAS,kBAAkB,QAAQ,MAAM,IAAI,KAAK,eAAe,SAAS;AAAA,MAC9E;AAAA,MACA,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,CAAC,EAAE;AACH,UAAM,YAAYA,QAAO,IAAI,EAAE,MAAM,KAAK,EAAE,CAAC,KAAK;AAClD,QAAI,aAAa,YAAa,QAAO,YAAY,SAAS;AAC1D,WAAO,WAAW,WAAW,UAAU,MAAM,GAAG,CAAC;AAAA,EACnD,QAAQ;AAGN,QAAI,YAAY,YAAa,QAAO,YAAY,QAAQ;AACxD,UAAM,WAAW,UAAU,MAAM,QAAQ;AACzC,QAAI,SAAU,QAAO,WAAW,UAAU,SAAS,MAAM,CAAC,CAAC;AAC3D,WAAO;AAAA,EACT;AACF;AACA,IAAM,WAAW;AACjB,SAAS,WAAW,UAAU,QAAQ;AACpC,QAAM,QAAQ,CAAC,OAAO,CAAC;AACvB,QAAM,UAAU,EAAE,OAAO,CAAC,KAAK;AAC/B,SAAO,YAAY,QAAQ,IAAI,QAAQ,IAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK;AACjF;;;ACvCO,IAAM,aAAN,MAAM,oBAAmB,KAAK;AAAA;AAAA,EAGnC,eAAe,MAAM;AACnB,UAAM;AACN,QAAI,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,UAAU;AAChE,WAAK,WAAW,KAAK,IAAI;AAAA,IAC3B;AACA,SAAK,WAAW,oBAAI,KAAK;AACzB,QAAI,MAAM,SAAS,KAAK,UAAU,IAAI,CAAC,GAAG;AACxC,WAAK,QAAQ,GAAG;AAAA,IAClB,OAAO;AACL,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,MACzB,WAAW,OAAO,KAAK,CAAC,MAAM,aAAa,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,WAAW;AACjH,aAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,MACtB,WAAW,OAAO,KAAK,CAAC,MAAM,UAAU;AACtC,aAAK,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC;AAAA,MACjC,WAAW,KAAK,CAAC,aAAa,MAAM;AAClC,aAAK,QAAQ,CAAC,KAAK,CAAC,CAAC;AAAA,MACvB,OAAO;AACL,aAAK,QAAQ,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;AAC/B,yBAAiB,MAAM,GAAG;AAC1B,uBAAe,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,GAAG,OAAO,MAAM;AACrB,WAAO,KAAK,SAAS,IAAI,YAAW,GAAG,MAAM,EAAE,IAAI,IAAI,YAAW,KAAK,IAAI,GAAG,EAAE;AAAA,EAClF;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU;AACrB,WAAO,IAAI,YAAW,CAAC,MAAM,QAAQ;AAAA,EACvC;AAAA,EACA,oBAAoB;AAClB,WAAO,CAAC,SAAS,KAAK,UAAU,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA,EAMA,QAAQ,MAAM;AACZ,SAAK,UAAU,QAAQ,MAAM,MAAM,SAAS;AAC5C,mBAAe,IAAI;AACnB,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA,EAMA,CAAC,OAAO,IAAI,mBAAmB,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,YAAW,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,QAAQ;AAAA,EACtD;AAAA;AAGF;AAGA,IAAM,KAAK;AACX,OAAO,oBAAoB,KAAK,SAAS,EAAE,QAAQ,YAAU;AAC3D,MAAI,CAAC,GAAG,KAAK,MAAM,EAAG;AACtB,QAAM,YAAY,OAAO,QAAQ,IAAI,OAAO;AAE5C,MAAI,CAAC,WAAW,UAAU,SAAS,EAAG;AACtC,MAAI,OAAO,WAAW,KAAK,GAAG;AAE5B,eAAW,UAAU,MAAM,IAAI,WAAY;AACzC,aAAO,KAAK,SAAS,SAAS,EAAE;AAAA,IAClC;AAAA,EACF,OAAO;AAEL,eAAW,UAAU,MAAM,IAAI,WAAY;AACzC,WAAK,UAAU,SAAS,EAAE,MAAM,KAAK,UAAU,SAAS;AACxD,uBAAiB,IAAI;AACrB,aAAO,CAAC;AAAA,IACV;AAGA,eAAW,UAAU,SAAS,IAAI,WAAY;AAC5C,WAAK,UAAU,SAAS,EAAE,MAAM,MAAM,SAAS;AAC/C,qBAAe,IAAI;AACnB,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF,CAAC;AAOD,SAAS,eAAe,MAAM;AAC5B,OAAK,SAAS,QAAQ,CAAC,IAAI;AAC3B,OAAK,SAAS,cAAc,KAAK,SAAS,cAAc,IAAI,KAAK,kBAAkB,CAAC;AACtF;AAQA,SAAS,iBAAiB,MAAM;AAE9B,OAAK,UAAU,YAAY,KAAK,MAAM,KAAK,SAAS,eAAe,GAAG,KAAK,SAAS,YAAY,GAAG,KAAK,SAAS,WAAW,CAAC;AAC7H,OAAK,UAAU,SAAS,KAAK,MAAM,KAAK,SAAS,YAAY,GAAG,KAAK,SAAS,cAAc,GAAG,KAAK,SAAS,cAAc,GAAG,KAAK,SAAS,mBAAmB,CAAC;AAGhK,mBAAiB,IAAI;AACvB;AAQA,SAAS,iBAAiB,MAAM;AAE9B,QAAM,SAAS,SAAS,KAAK,UAAU,IAAI;AA2B3C,QAAM,WAAW,oBAAI,KAAK,CAAC,IAAI;AAG/B,WAAS,YAAY,SAAS,YAAY,IAAI,CAAC;AAG/C,QAAM,eAAe,EAAC,oBAAI,KAAK,CAAC,IAAI,GAAE,kBAAkB;AACxD,QAAM,uBAAuB,EAAC,oBAAI,KAAK,CAAC,QAAQ,GAAE,kBAAkB;AACpE,QAAM,kBAAkB,eAAe;AAEvC,QAAM,WAAW,KAAK,UAAU,SAAS,MAAM,IAAI,MAAM,KAAK,SAAS,YAAY;AAGnF,MAAI,mBAAmB,SAAU,MAAK,SAAS,cAAc,KAAK,SAAS,cAAc,IAAI,eAAe;AAU5G,QAAM,aAAa,eAAe;AAClC,MAAI,WAAY,MAAK,UAAU,cAAc,KAAK,MAAM,KAAK,UAAU,cAAc,KAAK,IAAI,IAAI,UAAU;AAM5G,QAAM,aAAa,SAAS,KAAK,UAAU,IAAI;AAC/C,QAAM,mBAAmB,EAAC,oBAAI,KAAK,CAAC,IAAI,GAAE,kBAAkB;AAC5D,QAAM,iBAAiB,mBAAmB;AAC1C,QAAM,gBAAgB,eAAe;AACrC,QAAM,WAAW,iBAAiB;AAClC,MAAI,iBAAiB,UAAU;AAC7B,SAAK,UAAU,cAAc,KAAK,MAAM,KAAK,UAAU,cAAc,KAAK,IAAI,IAAI,QAAQ;AAK1F,UAAM,YAAY,SAAS,KAAK,UAAU,IAAI;AAC9C,UAAM,eAAe,aAAa;AAClC,QAAI,cAAc;AAChB,WAAK,SAAS,cAAc,KAAK,SAAS,cAAc,IAAI,YAAY;AACxE,WAAK,UAAU,cAAc,KAAK,MAAM,KAAK,UAAU,cAAc,KAAK,IAAI,IAAI,YAAY;AAAA,IAChG;AAAA,EACF;AAGF;;;AC5LO,IAAM,SAAN,MAAM,gBAAe,WAAW;AAAA;AAAA,EAGrC,OAAO,GAAG,OAAO,MAAM;AACrB,WAAO,KAAK,SAAS,IAAI,QAAO,GAAG,MAAM,EAAE,IAAI,IAAI,QAAO,KAAK,IAAI,GAAG,EAAE;AAAA,EAC1E;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,UAAM,CAAC,MAAM,OAAO,OAAO,IAAI,KAAK,aAAa;AACjD,UAAM,KAAK,GAAG,IAAI,GAAG,KAAK,IAAI,OAAO;AACrC,WAAO,KAAK,SAAS,YAAY,EAAE,MAAM,GAAG,EAAE,IAAI;AAAA,EACpD;AAAA,EACA,WAAW;AAET,WAAO,GAAG,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC;AAAA,EACtD;AAAA,EACA,eAAe;AAEb,UAAM,CAAC,KAAK,MAAM,OAAO,IAAI,IAAI,KAAK,SAAS,YAAY,EAAE,MAAM,GAAG;AAEtE,WAAO,GAAG,KAAK,MAAM,GAAG,EAAE,CAAkB,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;AAAA,EACvE;AAAA,EACA,eAAe;AAEb,UAAM,OAAO,KAAK,SAAS,YAAY,EAAE,MAAM,GAAG,EAAE,CAAC;AACrD,UAAM,CAAC,MAAM,OAAO,OAAO,IAAI,KAAK,aAAa;AAEjD,WAAO,GAAG,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,OAAO,KAAK,OAAO,KAAK,UAAU,IAAI,CAAC;AAAA,EAC7E;AAAA,EACA,eAAe,SAAS,SAAS;AAC/B,WAAO,KAAK,UAAU,eAAe,KAAK,MAAM,SAAS;AAAA,MACvD,GAAG;AAAA,MACH,UAAU,SAAS,YAAY,KAAK;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,KAAK,UAAU,mBAAmB,KAAK,MAAM,SAAS;AAAA,MAC3D,GAAG;AAAA,MACH,UAAU,SAAS,YAAY,KAAK;AAAA,IACtC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,SAAS,SAAS;AACnC,WAAO,KAAK,UAAU,mBAAmB,KAAK,MAAM,SAAS;AAAA,MAC3D,GAAG;AAAA,MACH,UAAU,SAAS,YAAY,KAAK;AAAA,IACtC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,UAAM,SAAS,KAAK,kBAAkB;AACtC,UAAM,OAAO,SAAS,IAAI,MAAM;AAChC,UAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI,EAAE,CAAC,EAAE,SAAS,GAAG,GAAG;AACvE,UAAM,UAAU,OAAO,KAAK,IAAI,MAAM,IAAI,EAAE,EAAE,SAAS,GAAG,GAAG;AAC7D,WAAO,CAAC,MAAM,OAAO,OAAO;AAAA,EAC9B;AAAA;AAAA,EAIA,aAAa,UAAU;AACrB,WAAO,IAAI,QAAO,CAAC,MAAM,QAAQ;AAAA,EACnC;AAAA;AAAA,EAIA,CAAC,OAAO,IAAI,mBAAmB,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,QAAO,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,QAAQ;AAAA,EAClD;AAAA;AAGF;AACA,SAAS,OAAO,IAAI,MAAM;AACxB,SAAO,IAAI,KAAK,eAAe,SAAS;AAAA,IACtC,UAAU;AAAA,IACV,cAAc;AAAA,EAChB,CAAC,EAAE,OAAO,IAAI,EAAE,MAAM,EAAE;AAC1B;;;ACxFA,IAAY;CAAZ,SAAYC,KAAE;AAEZ,EAAAA,IAAA,MAAA,IAAA;AAEA,EAAAA,IAAA,SAAA,IAAA;AAKA,EAAAA,IAAA,KAAA,IAAA;AAEA,EAAAA,IAAA,WAAA,IAAA;AAEA,EAAAA,IAAA,cAAA,IAAA;AAEA,EAAAA,IAAA,WAAA,IAAA;AAEA,EAAAA,IAAA,UAAA,IAAA;AAEA,EAAAA,IAAA,cAAA,IAAA;AAEA,EAAAA,IAAA,QAAA,IAAA;AAEA,EAAAA,IAAA,WAAA,IAAA;AAEA,EAAAA,IAAA,cAAA,IAAA;AAEA,EAAAA,IAAA,gBAAA,IAAA;AAEA,EAAAA,IAAA,OAAA,IAAA;AAEA,EAAAA,IAAA,QAAA,IAAA;AAEA,EAAAA,IAAA,KAAA,IAAA;AAMA,EAAAA,IAAA,iBAAA,IAAA;AAMA,EAAAA,IAAA,qBAAA,IAAA;AAEA,EAAAA,IAAA,MAAA,IAAA;AAEA,EAAAA,IAAA,OAAA,IAAA;AAEA,EAAAA,IAAA,SAAA,IAAA;AAEA,EAAAA,IAAA,UAAA,IAAA;AAEA,EAAAA,IAAA,YAAA,IAAA;AAEA,EAAAA,IAAA,kBAAA,IAAA;AAEA,EAAAA,IAAA,eAAA,IAAA;AACF,GA5DY,OAAA,KAAE,CAAA,EAAA;AA+Dd,IAAY;CAAZ,SAAYC,UAAO;AAEjB,EAAAA,SAAA,UAAA,IAAA;AAEA,EAAAA,SAAA,QAAA,IAAA;AAEA,EAAAA,SAAA,SAAA,IAAA;AAEA,EAAAA,SAAA,SAAA,IAAA;AAEA,EAAAA,SAAA,OAAA,IAAA;AACF,GAXY,YAAA,UAAO,CAAA,EAAA;AAiBnB,IAAY;CAAZ,SAAYC,iBAAc;AAExB,EAAAA,gBAAA,WAAA,IAAA;AAEA,EAAAA,gBAAA,cAAA,IAAA;AAEA,EAAAA,gBAAA,aAAA,IAAA;AAEA,EAAAA,gBAAA,UAAA,IAAA;AACF,GATY,mBAAA,iBAAc,CAAA,EAAA;AAe1B,IAAY;CAAZ,SAAYC,YAAS;AAEnB,EAAAA,WAAA,oBAAA,IAAA;AAEA,EAAAA,WAAA,mBAAA,IAAA;AAEA,EAAAA,WAAA,mBAAA,IAAA;AAEA,EAAAA,WAAA,kBAAA,IAAA;AAEA,EAAAA,WAAA,qBAAA,IAAA;AAEA,EAAAA,WAAA,oBAAA,IAAA;AAEA,EAAAA,WAAA,sBAAA,IAAA;AAEA,EAAAA,WAAA,qBAAA,IAAA;AACF,GAjBY,cAAA,YAAS,CAAA,EAAA;;;ACvGrB,IAAM,aAAa;AACnB,IAAM,aAAa;AAcb,SAAU,yBAAyB,OAAaC,UAAgB;AAEpE,QAAM,kBAAkBA,SAAQ,aAAa,KAAK;AAGlD,QAAM,iBACJ,gBAAgB,OAAM,IAAK,IAAI,gBAAgB,OAAM,IAAK;AAE5D,QAAM,qBAAqBA,SAAQ,QAAQ,OAAO,CAAC,iBAAiB,CAAC;AAErE,QAAM,qBAAqBA,SAAQ,QACjC,oBACA,aAAa,IAAI,CAAC;AAEpB,QAAM,gBACJA,SAAQ,SAAS,KAAK,MAAMA,SAAQ,SAAS,kBAAkB,IAC3D,aACA;AAEN,SAAO;AACT;;;ACvBM,SAAU,qBAAqB,MAAYC,UAAgB;AAC/D,QAAM,eAAeA,SAAQ,aAAa,IAAI;AAC9C,QAAM,YAAY,aAAa,OAAM;AAErC,MAAI,cAAc,GAAG;AACnB,WAAO;EACT,WAAW,cAAc,GAAG;AAC1B,WAAOA,SAAQ,QAAQ,cAAc,KAAK,CAAC;EAC7C,OAAO;AACL,WAAOA,SAAQ,QAAQ,cAAc,MAAM,YAAY,EAAE;EAC3D;AACF;;;ACTM,SAAU,mBAAmB,MAAYC,UAAgB;AAC7D,QAAM,YAAY,qBAAqB,MAAMA,QAAO;AACpD,QAAM,gBAAgB,yBAAyB,MAAMA,QAAO;AAC5D,QAAM,UAAUA,SAAQ,QAAQ,WAAW,gBAAgB,IAAI,CAAC;AAChE,SAAO;AACT;;;ACmFM,IAAO,UAAP,MAAc;;;;;;;EAalB,YACE,SACA,WAA6C;AA0D/C,SAAA,OAAoB;AAQpB,SAAA,QAAQ,MAAW;AACjB,UAAI,KAAK,WAAW,OAAO;AACzB,eAAO,KAAK,UAAU,MAAK;MAC7B;AACA,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,OAAO,GAAG,KAAK,QAAQ,QAAQ;MACxC;AACA,aAAO,IAAI,KAAK,KAAI;IACtB;AAWA,SAAA,UAAU,CAAC,MAAc,YAAoB,SAAsB;AACjE,UAAI,KAAK,WAAW,SAAS;AAC3B,eAAO,KAAK,UAAU,QAAQ,MAAM,YAAY,IAAI;MACtD;AACA,UAAI,KAAK,QAAQ,UAAU;AACzB,eAAO,IAAI,OAAO,MAAM,YAAY,MAAM,KAAK,QAAQ,QAAQ;MACjE;AACA,aAAO,IAAI,KAAK,MAAM,YAAY,IAAI;IACxC;AASA,SAAA,UAAU,CAAC,MAAY,WAAwB;AAC7C,aAAO,KAAK,WAAW,UACnB,KAAK,UAAU,QAAQ,MAAM,MAAM,IACnC,QAAQ,MAAM,MAAM;IAC1B;AASA,SAAA,YAAY,CAAC,MAAY,WAAwB;AAC/C,aAAO,KAAK,WAAW,YACnB,KAAK,UAAU,UAAU,MAAM,MAAM,IACrC,UAAU,MAAM,MAAM;IAC5B;AASA,SAAA,WAAW,CAAC,MAAY,WAAwB;AAC9C,aAAO,KAAK,WAAW,WACnB,KAAK,UAAU,SAAS,MAAM,MAAM,IACpC,SAAS,MAAM,MAAM;IAC3B;AASA,SAAA,WAAW,CAAC,MAAY,WAAwB;AAC9C,aAAO,KAAK,WAAW,WACnB,KAAK,UAAU,SAAS,MAAM,MAAM,IACpC,SAAS,MAAM,MAAM;IAC3B;AASA,SAAA,2BAA2B,CAAC,UAAgB,cAA2B;AACrE,aAAO,KAAK,WAAW,2BACnB,KAAK,UAAU,yBAAyB,UAAU,SAAS,IAC3D,yBAAyB,UAAU,SAAS;IAClD;AASA,SAAA,6BAA6B,CAAC,UAAgB,cAA2B;AACvE,aAAO,KAAK,WAAW,6BACnB,KAAK,UAAU,2BAA2B,UAAU,SAAS,IAC7D,2BAA2B,UAAU,SAAS;IACpD;AAOA,SAAA,sBAAsB,CAAC,aAA8B;AACnD,aAAO,KAAK,WAAW,sBACnB,KAAK,UAAU,oBAAoB,QAAQ,IAC3C,oBAAoB,QAAQ;IAClC;AAQA,SAAA,qBAAqB,CAAC,SAAoB;AACxC,aAAO,KAAK,WAAW,qBACnB,KAAK,UAAU,mBAAmB,IAAI,IACtC,mBAAmB,MAAM,IAAI;IACnC;AAQA,SAAA,eAAe,CAAC,SAAoB;AAClC,aAAO,KAAK,WAAW,eACnB,KAAK,UAAU,aAAa,IAAI,IAChC,aAAa,IAAI;IACvB;AAQA,SAAA,aAAa,CAAC,SAAoB;AAChC,aAAO,KAAK,WAAW,aACnB,KAAK,UAAU,WAAW,IAAI,IAC9B,WAAW,IAAI;IACrB;AAQA,SAAA,YAAY,CAAC,MAAYC,aAA0C;AACjE,aAAO,KAAK,WAAW,YACnB,KAAK,UAAU,UAAU,MAAMA,QAAO,IACtC,UAAU,MAAM,KAAK,OAAO;IAClC;AAQA,SAAA,YAAY,CAAC,SAAoB;AAC/B,aAAO,KAAK,WAAW,YACnB,KAAK,UAAU,UAAU,IAAI,IAC7B,UAAU,IAAI;IACpB;AASA,SAAA,SAAS,CACP,MACA,WACAA,aACU;AACV,YAAM,YAAY,KAAK,WAAW,SAC9B,KAAK,UAAU,OAAO,MAAM,WAAW,KAAK,OAAO,IACnD,OAAO,MAAM,WAAW,KAAK,OAAO;AACxC,UAAI,KAAK,QAAQ,YAAY,KAAK,QAAQ,aAAa,QAAQ;AAC7D,eAAO,KAAK,cAAc,SAAS;MACrC;AACA,aAAO;IACT;AAQA,SAAA,aAAa,CAAC,SAAsB;AAClC,aAAO,KAAK,WAAW,aACnB,KAAK,UAAU,WAAW,IAAI,IAC9B,WAAW,IAAI;IACrB;AAQA,SAAA,WAAW,CAAC,MAAYA,aAAqC;AAC3D,aAAO,KAAK,WAAW,WACnB,KAAK,UAAU,SAAS,MAAM,KAAK,OAAO,IAC1C,SAAS,MAAM,KAAK,OAAO;IACjC;AAQA,SAAA,UAAU,CAAC,MAAYA,aAAoC;AACzD,aAAO,KAAK,WAAW,UACnB,KAAK,UAAU,QAAQ,MAAM,KAAK,OAAO,IACzC,QAAQ,MAAM,KAAK,OAAO;IAChC;AAQA,SAAA,UAAU,CAAC,MAAYA,aAAoC;AACzD,aAAO,KAAK,WAAW,UACnB,KAAK,UAAU,QAAQ,MAAM,KAAK,OAAO,IACzC,QAAQ,MAAM,KAAK,OAAO;IAChC;AASA,SAAA,UAAU,CAAC,MAAY,kBAAgC;AACrD,aAAO,KAAK,WAAW,UACnB,KAAK,UAAU,QAAQ,MAAM,aAAa,IAC1C,QAAQ,MAAM,aAAa;IACjC;AASA,SAAA,WAAW,CAAC,MAAY,kBAAgC;AACtD,aAAO,KAAK,WAAW,WACnB,KAAK,UAAU,SAAS,MAAM,aAAa,IAC3C,SAAS,MAAM,aAAa;IAClC;AAQA,SAAA,SAA4C,CAAC,UAAwB;AACnE,aAAO,KAAK,WAAW,SACnB,KAAK,UAAU,OAAO,KAAK,IAC3B,OAAO,KAAK;IAClB;AASA,SAAA,YAAY,CAAC,UAAgB,cAA4B;AACvD,aAAO,KAAK,WAAW,YACnB,KAAK,UAAU,UAAU,UAAU,SAAS,IAC5C,UAAU,UAAU,SAAS;IACnC;AASA,SAAA,cAAc,CAAC,UAAgB,cAA4B;AACzD,aAAO,KAAK,WAAW,cACnB,KAAK,UAAU,YAAY,UAAU,SAAS,IAC9C,YAAY,UAAU,SAAS;IACrC;AASA,SAAA,aAAa,CAAC,UAAgB,cAA4B;AACxD,aAAO,KAAK,WAAW,aACnB,KAAK,UAAU,WAAW,UAAU,SAAS,IAC7C,WAAW,UAAU,SAAS;IACpC;AAQA,SAAA,MAAM,CAAC,UAAuB;AAC5B,aAAO,KAAK,WAAW,MAAM,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,KAAK;IACpE;AAQA,SAAA,MAAM,CAAC,UAAuB;AAC5B,aAAO,KAAK,WAAW,MAAM,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI,KAAK;IACpE;AASA,SAAA,WAAW,CAAC,MAAY,UAAuB;AAC7C,aAAO,KAAK,WAAW,WACnB,KAAK,UAAU,SAAS,MAAM,KAAK,IACnC,SAAS,MAAM,KAAK;IAC1B;AASA,SAAA,UAAU,CAAC,MAAY,SAAsB;AAC3C,aAAO,KAAK,WAAW,UACnB,KAAK,UAAU,QAAQ,MAAM,IAAI,IACjC,QAAQ,MAAM,IAAI;IACxB;AAQA,SAAA,uBAAuB,CAAC,MAAYC,aAA0B;AAC5D,aAAO,KAAK,WAAW,uBACnB,KAAK,UAAU,qBAAqB,MAAM,IAAI,IAC9C,qBAAqB,MAAM,IAAI;IACrC;AAQA,SAAA,aAAa,CAAC,SAAoB;AAChC,aAAO,KAAK,WAAW,aACnB,KAAK,UAAU,WAAW,IAAI,IAC9B,WAAW,IAAI;IACrB;AAQA,SAAA,iBAAiB,CAAC,SAAoB;AACpC,aAAO,KAAK,WAAW,iBACnB,KAAK,UAAU,eAAe,IAAI,IAClC,eAAe,IAAI;IACzB;AAQA,SAAA,eAAe,CAAC,SAAoB;AAClC,aAAO,KAAK,WAAW,eACnB,KAAK,UAAU,aAAa,IAAI,IAChC,aAAa,IAAI;IACvB;AAQA,SAAA,cAAc,CAAC,MAAYD,aAAsC;AAC/D,aAAO,KAAK,WAAW,cACnB,KAAK,UAAU,YAAY,MAAM,KAAK,OAAO,IAC7C,YAAY,MAAM,KAAK,OAAO;IACpC;AAQA,SAAA,cAAc,CAAC,SAAoB;AACjC,aAAO,KAAK,WAAW,cACnB,KAAK,UAAU,YAAY,IAAI,IAC/B,YAAY,IAAI;IACtB;AAxfE,SAAK,UAAU,EAAE,QAAQ,MAAM,GAAG,QAAO;AACzC,SAAK,YAAY;EACnB;;;;;;;;EASQ,cAAW;AACjB,UAAM,EAAE,WAAW,OAAM,IAAK,KAAK;AAGnC,UAAM,YAAY,IAAI,KAAK,aAAa,SAAS;MAC/C,iBAAiB;KAClB;AAGD,UAAM,WAAmC,CAAA;AACzC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,eAAS,EAAE,SAAQ,CAAE,IAAI,UAAU,OAAO,CAAC;IAC7C;AAEA,WAAO;EACT;;;;;;;;EASQ,cAAc,OAAa;AACjC,UAAM,WAAW,KAAK,YAAW;AACjC,WAAO,MAAM,QAAQ,OAAO,CAAC,UAAU,SAAS,KAAK,KAAK,KAAK;EACjE;;;;;;;;EASA,aAAa,OAAsB;AACjC,WAAO,KAAK,cAAc,MAAM,SAAQ,CAAE;EAC5C;;AAidK,IAAM,iBAAiB,IAAI,QAAO;AAMlC,IAAM,UAAU;;;ACxnBjB,IAAO,cAAP,MAAkB;EACtB,YACE,MACA,cACAE,WAAmB,gBAAc;AAEjC,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,UAAU,QACb,gBAAgB,CAACA,SAAQ,YAAY,MAAM,YAAY,CAAC;AAE1D,SAAK,UAAUA;EACjB;;;;;;;;EAoCA,UAAU,KAAgB;AACxB,WACE,KAAK,QAAQ,UAAU,IAAI,MAAM,KAAK,IAAI,KAC1C,KAAK,QAAQ,YAAY,IAAI,cAAc,KAAK,YAAY;EAEhE;;;;ACtDI,IAAO,gBAAP,MAAoB;EACxB,YAAY,OAAa,OAAqB;AAC5C,SAAK,OAAO;AACZ,SAAK,QAAQ;EACf;;;;ACLI,IAAO,eAAP,MAAmB;EACvB,YAAY,YAAoB,MAAmB;AACjD,SAAK,OAAO;AACZ,SAAK,aAAa;EACpB;;;;ACGI,SAAU,kBACd,OACA,MACA,cAAc,OACdC,WAAU,gBAAc;AAExB,MAAI,EAAE,MAAM,GAAE,IAAK;AACnB,QAAM,EAAE,0BAAAC,2BAA0B,WAAAC,WAAS,IAAKF;AAChD,MAAI,QAAQ,IAAI;AACd,UAAM,kBAAkBC,0BAAyB,IAAI,IAAI,IAAI;AAC7D,QAAI,iBAAiB;AACnB,OAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;IACxB;AACA,UAAM,YACJA,0BAAyB,MAAM,IAAI,MAAM,cAAc,IAAI,MAC3DA,0BAAyB,IAAI,IAAI,MAAM,cAAc,IAAI;AAC3D,WAAO;EACT;AACA,MAAI,CAAC,eAAe,IAAI;AACtB,WAAOC,WAAU,IAAI,IAAI;EAC3B;AACA,MAAI,CAAC,eAAe,MAAM;AACxB,WAAOA,WAAU,MAAM,IAAI;EAC7B;AACA,SAAO;AACT;AAMO,IAAM,gBAAgB,CAAC,OAAkB,SAC9C,kBAAkB,OAAO,MAAM,OAAO,cAAc;;;AC9BhD,SAAU,eAAe,SAAgB;AAC7C,SAAO,QACL,WACE,OAAO,YAAY,YACnB,YAAY,WACZ,WAAW,OAAO;AAExB;AASM,SAAU,YAAY,OAAc;AACxC,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,UAAU,KAAK;AACtE;AASM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,WAAW,KAAK;AACvE;AASM,SAAU,iBAAiB,OAAc;AAC7C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,YAAY,KAAK;AACxE;AASM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,QAAQ,SAAS,OAAO,UAAU,YAAY,eAAe,KAAK;AAC3E;AAUM,SAAU,aACd,OACAC,UAAgB;AAEhB,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAMA,SAAQ,MAAM;AAC3D;;;AC5DM,SAAU,mBACd,MACA,UACAC,WAAmB,gBAAc;AAEjC,QAAM,cAAc,CAAC,MAAM,QAAQ,QAAQ,IAAI,CAAC,QAAQ,IAAI;AAC5D,QAAM,EAAE,WAAAC,YAAW,0BAAAC,2BAA0B,SAAAC,SAAO,IAAKH;AACzD,SAAO,YAAY,KAAK,CAAC,YAAoB;AAC3C,QAAI,OAAO,YAAY,WAAW;AAChC,aAAO;IACT;AACA,QAAIA,SAAQ,OAAO,OAAO,GAAG;AAC3B,aAAOC,WAAU,MAAM,OAAO;IAChC;AACA,QAAI,aAAa,SAASD,QAAO,GAAG;AAClC,aAAO,QAAQ,SAAS,IAAI;IAC9B;AACA,QAAI,YAAY,OAAO,GAAG;AACxB,aAAO,kBAAkB,SAAS,MAAM,OAAOA,QAAO;IACxD;AACA,QAAI,gBAAgB,OAAO,GAAG;AAC5B,UAAI,CAAC,MAAM,QAAQ,QAAQ,SAAS,GAAG;AACrC,eAAO,QAAQ,cAAc,KAAK,OAAM;MAC1C;AACA,aAAO,QAAQ,UAAU,SAAS,KAAK,OAAM,CAAE;IACjD;AACA,QAAI,eAAe,OAAO,GAAG;AAC3B,YAAM,aAAaE,0BAAyB,QAAQ,QAAQ,IAAI;AAChE,YAAM,YAAYA,0BAAyB,QAAQ,OAAO,IAAI;AAC9D,YAAM,cAAc,aAAa;AACjC,YAAM,aAAa,YAAY;AAC/B,YAAM,mBAAmBC,SAAQ,QAAQ,QAAQ,QAAQ,KAAK;AAC9D,UAAI,kBAAkB;AACpB,eAAO,cAAc;MACvB,OAAO;AACL,eAAO,eAAe;MACxB;IACF;AACA,QAAI,gBAAgB,OAAO,GAAG;AAC5B,aAAOD,0BAAyB,MAAM,QAAQ,KAAK,IAAI;IACzD;AACA,QAAI,iBAAiB,OAAO,GAAG;AAC7B,aAAOA,0BAAyB,QAAQ,QAAQ,IAAI,IAAI;IAC1D;AACA,QAAI,OAAO,YAAY,YAAY;AACjC,aAAO,QAAQ,IAAI;IACrB;AACA,WAAO;EACT,CAAC;AACH;AAMO,IAAM,UAAU;;;AC3DjB,SAAU,mBACd,MACA,OACA,UACA,QACAE,UAAgB;AAEhB,QAAM,EACJ,UACA,QACA,WACA,iBACA,mBACA,MAAK,IACH;AAEJ,QAAM,EACJ,WAAAC,YACA,aAAAC,cACA,cAAAC,eACA,UAAAC,WACA,YAAAC,aACA,SAAAC,SAAO,IACLN;AAEJ,QAAM,mBAAmB,YAAYG,cAAa,QAAQ;AAC1D,QAAM,iBAAiB,UAAUE,YAAW,MAAM;AAElD,QAAM,uBAAuD;IAC3D,CAAC,QAAQ,OAAO,GAAG,CAAA;IACnB,CAAC,QAAQ,OAAO,GAAG,CAAA;IACnB,CAAC,QAAQ,QAAQ,GAAG,CAAA;IACpB,CAAC,QAAQ,MAAM,GAAG,CAAA;IAClB,CAAC,QAAQ,KAAK,GAAG,CAAA;;AAGnB,QAAM,qBAAoD,CAAA;AAE1D,aAAW,OAAO,MAAM;AACtB,UAAM,EAAE,MAAM,aAAY,IAAK;AAE/B,UAAM,YAAY,QAAQ,gBAAgB,CAACH,aAAY,MAAM,YAAY,CAAC;AAE1E,UAAM,mBAAmB,QACvB,oBAAoBE,UAAS,MAAM,gBAAgB,CAAC;AAGtD,UAAM,gBAAgB,QACpB,kBAAkBE,SAAQ,MAAM,cAAc,CAAC;AAGjD,UAAM,aAAa,QACjB,YAAY,mBAAmB,MAAM,UAAUN,QAAO,CAAC;AAGzD,UAAM,WACJ,QAAQ,UAAU,mBAAmB,MAAM,QAAQA,QAAO,CAAC,KAC3D,oBACA;IAEC,CAAC,qBAAqB,CAAC,mBAAmB,aAC1C,qBAAqB,oBAAoB,SAAS;AAErD,UAAM,UAAUC,WAAU,MAAM,SAASD,SAAQ,MAAK,CAAE;AAExD,QAAI;AAAW,2BAAqB,QAAQ,KAAK,GAAG;AACpD,QAAI;AAAY,2BAAqB,SAAS,KAAK,GAAG;AACtD,QAAI;AAAU,2BAAqB,OAAO,KAAK,GAAG;AAClD,QAAI;AAAS,2BAAqB,MAAM,KAAK,GAAG;AAGhD,QAAI,WAAW;AACb,aAAO,KAAK,SAAS,EAAE,QAAQ,CAAC,SAAQ;AACtC,cAAM,gBAAgB,YAAY,IAAI;AACtC,cAAMO,WAAU,gBACZ,mBAAmB,MAAM,eAAeP,QAAO,IAC/C;AACJ,YAAI,CAACO;AAAS;AACd,YAAI,mBAAmB,IAAI,GAAG;AAC5B,6BAAmB,IAAI,EAAE,KAAK,GAAG;QACnC,OAAO;AACL,6BAAmB,IAAI,IAAI,CAAC,GAAG;QACjC;MACF,CAAC;IACH;EACF;AAEA,SAAO,CAAC,QAA+B;AAErC,UAAM,WAAqC;MACzC,CAAC,QAAQ,OAAO,GAAG;MACnB,CAAC,QAAQ,QAAQ,GAAG;MACpB,CAAC,QAAQ,MAAM,GAAG;MAClB,CAAC,QAAQ,OAAO,GAAG;MACnB,CAAC,QAAQ,KAAK,GAAG;;AAEnB,UAAM,kBAA6B,CAAA;AAGnC,eAAW,QAAQ,sBAAsB;AACvC,YAAMC,QAAO,qBAAqB,IAAe;AACjD,eAAS,IAAe,IAAIA,MAAK,KAAK,CAAC,MAAM,MAAM,GAAG;IACxD;AACA,eAAW,QAAQ,oBAAoB;AACrC,sBAAgB,IAAI,IAAI,mBAAmB,IAAI,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG;IACxE;AAEA,WAAO;MACL,GAAG;;MAEH,GAAG;;EAEP;AACF;;;ACpHM,SAAU,0BACd,WACA,YACA,sBAA2C,CAAA,GAAE;AAE7C,QAAM,qBAAqB,OAAO,QAAQ,SAAS,EAChD,OAAO,CAAC,CAAC,EAAE,MAAM,MAAM,WAAW,IAAI,EACtC,OACC,CAAC,eAAe,CAAC,GAAG,MAAK;AACvB,QAAI,oBAAoB,GAAG,GAAG;AAC5B,oBAAc,KAAK,oBAAoB,GAAa,CAAC;IACvD,WAAW,WAAW,QAAQ,GAAc,CAAC,GAAG;AAC9C,oBAAc,KAAK,WAAW,QAAQ,GAAc,CAAC,CAAC;IACxD,WAAW,WAAW,eAAe,GAAqB,CAAC,GAAG;AAC5D,oBAAc,KAAK,WAAW,eAAe,GAAqB,CAAC,CAAC;IACtE;AACA,WAAO;EACT,GACA,CAAC,WAAW,GAAG,GAAG,CAAC,CAAa;AAGpC,SAAO;AACT;;;ACrCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA,mBAAiD;AAQ3C,SAAU,OAAO,OAA8C;AACnE,SAAO,aAAAC,QAAA,cAAA,UAAA,EAAA,GAAY,MAAK,CAAA;AAC1B;;;ACVA,IAAAC,gBAA2C;AAQrC,SAAU,aAAa,OAAsC;AACjE,SAAO,cAAAC,QAAA,cAAA,QAAA,EAAA,GAAU,MAAK,CAAA;AACxB;;;ACVA,IAAAC,gBAAkB;AAQZ,SAAU,QAAQ,OAYvB;AACC,QAAM,EAAE,OAAO,IAAI,cAAc,QAAQ,UAAS,IAAK;AAEvD,SACE,cAAAC,QAAA;IAAA;IAAA,EAAK,WAAsB,OAAO,MAAM,QAAQ,MAAM,SAAQ,YAAW;IACtE,gBAAgB,QACf,cAAAA,QAAA,cAAA,WAAA,EAAS,QAAO,sDAAqD,CAAA;IAEtE,gBAAgB,UACf,cAAAA,QAAA,cAAA,WAAA,EAAS,QAAO,mDAAkD,CAAA;IAEnE,gBAAgB,UACf,cAAAA,QAAA,cAAA,WAAA,EAAS,QAAO,wEAAuE,CAAA;IAExF,gBAAgB,WACf,cAAAA,QAAA,cAAA,WAAA,EAAS,QAAO,wEAAuE,CAAA;EACxF;AAGP;;;ACvCA,IAAAC,gBAA2C;AAerC,SAAU,IACd,OAKkC;AAElC,QAAM,EAAE,KAAK,WAAW,GAAG,QAAO,IAAK;AACvC,SAAO,cAAAC,QAAA,cAAA,MAAA,EAAA,GAAQ,QAAO,CAAA;AACxB;;;ACzBA,IAAAC,gBAAiD;AAW3C,SAAU,UACd,OAK2C;AAE3C,QAAM,EAAE,KAAK,WAAW,GAAG,YAAW,IAAK;AAE3C,QAAM,MAAM,cAAAC,QAAM,OAA0B,IAAI;AAChD,gBAAAA,QAAM,UAAU,MAAK;AACnB,QAAI,UAAU;AAAS,UAAI,SAAS,MAAK;EAC3C,GAAG,CAAC,UAAU,OAAO,CAAC;AACtB,SAAO,cAAAA,QAAA,cAAA,UAAA,EAAQ,KAAQ,GAAM,YAAW,CAAA;AAC1C;;;AC1BA,IAAAC,gBAAiD;AAqB3C,SAAU,SACd,OAa6D;AAE7D,QAAM,EAAE,SAAS,WAAW,YAAY,YAAY,GAAG,YAAW,IAAK;AAEvE,QAAM,iBAAiB,CAAC,WAAW,GAAG,QAAQ,GAAG,SAAS,EAAE,KAAK,GAAG;AAEpE,QAAM,iBAAiB,SAAS,KAC9B,CAAC,EAAE,MAAK,MAAO,UAAU,YAAY,KAAK;AAE5C,SACE,cAAAC,QAAA;IAAA;IAAA,EAAA,iBACiB,YAAY,UAC3B,WAAW,WAAW,GAAG,YAAY,EAAC;IAEtC,cAAAA,QAAA,cAAC,WAAW,QAAM,EAAC,WAAW,gBAAc,GAAM,YAAW,GAC1D,SAAS,IAAI,CAAC,EAAE,OAAO,OAAO,SAAQ,MACrC,cAAAA,QAAA,cAAC,WAAW,QAAM,EAAC,KAAK,OAAO,OAAc,SAAkB,GAC5D,KAAK,CAET,CAAC;IAEJ,cAAAA,QAAA;MAAA;MAAA,EAAM,WAAW,WAAW,GAAG,YAAY,GAAC,eAAA,KAAA;MACzC,gBAAgB;MACjB,cAAAA,QAAA,cAAC,WAAW,SAAO,EACjB,aAAY,QACZ,MAAM,IACN,WAAW,WAAW,GAAG,OAAO,EAAC,CAAA;IACjC;EACG;AAGb;;;AClEA,IAAAC,gBAA2C;AAQrC,SAAU,YAAY,OAAqC;AAC/D,SAAO,cAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,MAAK,CAAA;AACvB;;;ACVA,IAAAC,gBAA2C;AAQrC,SAAU,OAAO,OAAqC;AAC1D,SAAO,cAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,MAAK,CAAA;AACvB;;;ACVA,IAAAC,gBAA2C;AAWrC,SAAU,MACd,OAKkC;AAElC,QAAM,EAAE,eAAe,cAAc,GAAG,SAAQ,IAAK;AACrD,SAAO,cAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,SAAQ,GAAG,MAAM,QAAQ;AAC3C;;;ACrBA,IAAAC,iBAA2C;AAUrC,SAAU,aACd,OAKkC;AAElC,QAAM,EAAE,eAAe,cAAc,GAAG,SAAQ,IAAK;AACrD,SAAO,eAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,SAAQ,CAAA;AAC1B;;;ACpBA,IAAAC,iBAAgD;AAQ1C,SAAU,UAAU,OAA4C;AACpE,SAAO,eAAAC,QAAA,cAAA,SAAA,EAAA,GAAW,MAAK,CAAA;AACzB;;;ACVA,IAAAC,iBAA2C;AAQrC,SAAU,OAAO,OAAqC;AAC1D,SAAO,eAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,MAAK,CAAA;AACvB;;;ACVA,IAAAC,iBAAkB;;;ACAlB,IAAAC,iBAA0C;AAiBnC,IAAM,uBAAmB,8BAM9B,MAAS;AA2DL,SAAU,eAAY;AAG1B,QAAM,cAAU,2BAAW,gBAAgB;AAC3C,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI,MAAM,wDAAwD;EAC1E;AACA,SAAO;AACT;;;AD9EM,SAAU,eAAe,OAAoB;AACjD,QAAM,EAAE,WAAU,IAAK,aAAY;AACnC,SAAO,eAAAC,QAAA,cAAC,WAAW,UAAQ,EAAA,GAAK,MAAK,CAAA;AACvC;;;AEfA,IAAAC,iBAIO;AAWD,SAAU,IACd,OAS+B;AAE/B,QAAM,EACJ,iBACA,aACA,eACA,WACA,GAAG,SAAQ,IACT;AAEJ,QAAM,EACJ,YACA,YACA,QAAQ,EAAE,eAAAC,gBAAe,WAAAC,WAAS,EAAE,IAClC,aAAY;AAEhB,QAAM,sBAAkB,4BACtB,CAAC,MAA0C;AACzC,QAAI,WAAW;AACb,oBAAc,CAAC;IACjB;EACF,GACA,CAAC,WAAW,WAAW,CAAC;AAG1B,QAAM,0BAAsB,4BAC1B,CAAC,MAA0C;AACzC,QAAI,eAAe;AACjB,wBAAkB,CAAC;IACrB;EACF,GACA,CAAC,eAAe,eAAe,CAAC;AAGlC,SACE,eAAAC,QAAA;IAAA;IAAA,EAAA,GAAS,SAAQ;IACf,eAAAA,QAAA;MAAC,WAAW;MAAmB,EAC7B,MAAK,UACL,WAAW,WAAW,GAAG,mBAAmB,GAC5C,UAAU,gBAAgB,SAAY,IAAE,iBACzB,gBAAgB,SAAY,MAAI,cACnCF,eAAc,aAAa,GACvC,SAAS,oBAAmB;MAE5B,eAAAE,QAAA,cAAC,WAAW,SAAO,EACjB,UAAU,gBAAgB,SAAY,MACtC,WAAW,WAAW,GAAG,OAAO,GAChC,aAAY,OAAM,CAAA;IAClB;IAEJ,eAAAA,QAAA;MAAC,WAAW;MAAe,EACzB,MAAK,UACL,WAAW,WAAW,GAAG,eAAe,GACxC,UAAU,YAAY,SAAY,IAAE,iBACrB,YAAY,SAAY,MAAI,cAC/BD,WAAU,SAAS,GAC/B,SAAS,gBAAe;MAExB,eAAAC,QAAA,cAAC,WAAW,SAAO,EACjB,UAAU,YAAY,SAAY,MAClC,aAAY,SACZ,WAAW,WAAW,GAAG,OAAO,EAAC,CAAA;IACjC;EACyB;AAGnC;;;AC3FA,IAAAC,iBAAiD;AAU3C,SAAU,gBACd,OAA8C;AAE9C,QAAM,EAAE,WAAU,IAAK,aAAY;AACnC,SAAO,eAAAC,QAAA,cAAC,WAAW,QAAM,EAAA,GAAK,MAAK,CAAA;AACrC;;;ACfA,IAAAC,iBAAiD;AAQ3C,SAAU,OAAO,OAA8C;AACnE,SAAO,eAAAC,QAAA,cAAA,UAAA,EAAA,GAAY,MAAK,CAAA;AAC1B;;;ACVA,IAAAC,iBAAiD;AAU3C,SAAU,oBACd,OAA8C;AAE9C,QAAM,EAAE,WAAU,IAAK,aAAY;AACnC,SAAO,eAAAC,QAAA,cAAC,WAAW,QAAM,EAAA,GAAK,MAAK,CAAA;AACrC;;;ACfA,IAAAC,iBAAqD;AAQ/C,SAAU,KACd,OAGkC;AAElC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,SAAO,eAAAC,QAAA,cAAA,OAAA,EAAA,GAAS,MAAM,KAAK,QAAO,CAAA;AACpC;;;AChBA,IAAAC,iBAAiD;AAQ3C,SAAU,OAAO,OAA8C;AACnE,SAAO,eAAAC,QAAA,cAAA,UAAA,EAAA,GAAY,MAAK,CAAA;AAC1B;;;ACVA,IAAAC,iBAA2C;AAUrC,SAAU,KACd,OAGuC;AAEvC,QAAM,EAAE,MAAM,GAAG,QAAO,IAAK;AAC7B,SAAO,eAAAC,QAAA,cAAA,MAAA,EAAA,GAAQ,QAAO,CAAA;AACxB;;;AClBA,IAAAC,iBAA6C;AAQvC,SAAU,QAAQ,OAA6C;AACnE,SAAO,eAAAC,QAAA,cAAA,MAAA,EAAA,GAAQ,MAAK,CAAA;AACtB;;;ACVA,IAAAC,iBAA2C;AAQrC,SAAU,SAAS,OAA0C;AACjE,SACE,eAAAC,QAAA;IAAA;IAAA,EAAA,eAAA,KAAA;IACE,eAAAA,QAAA,cAAA,MAAA,EAAA,GAAQ,MAAK,CAAA;EAAI;AAGvB;;;ACdA,IAAAC,iBAA6C;AAUvC,SAAU,WACd,OAG0C;AAE1C,QAAM,EAAE,MAAM,GAAG,QAAO,IAAK;AAC7B,SAAO,eAAAC,QAAA,cAAA,MAAA,EAAA,GAAQ,QAAO,CAAA;AACxB;;;AClBA,IAAAC,iBAA6C;AAQvC,SAAU,iBACd,OAA6C;AAE7C,SAAO,eAAAC,QAAA,cAAA,MAAA,EAAA,GAAQ,MAAK,CAAA;AACtB;;;ACZA,IAAAC,iBAA2C;AAQrC,SAAU,MAAM,OAA8C;AAClE,SAAO,eAAAC,QAAA,cAAA,SAAA,EAAA,GAAW,MAAK,CAAA;AACzB;;;ACVA,IAAAC,iBAAkB;AAYZ,SAAU,cAAc,OAAoB;AAChD,QAAM,EAAE,WAAU,IAAK,aAAY;AACnC,SAAO,eAAAC,QAAA,cAAC,WAAW,UAAQ,EAAA,GAAK,MAAK,CAAA;AACvC;;;ACFM,SAAU,cACd,kBAA8C;AAE9C,SAAO;IACL,GAAG;IACH,GAAG;;AAEP;;;ACTM,SAAU,kBACd,OAAqB;AAErB,QAAM,iBAA0C;IAC9C,aAAa,MAAM,QAAQ;IAC3B,iBAAiB,cAAc,QAAQ,MAAM,WAAW;IACxD,wBACG,MAAM,kBAAkB,MAAM,iBAAiB,KAAM;IACxD,qBAAqB,MAAM,kBAAkB;IAC7C,2BAA2B,MAAM,qBAAqB;IACtD,mBAAmB,MAAM,aAAa;;AAExC,SAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAK;AAC3C,QAAI,IAAI,WAAW,OAAO,GAAG;AAC3B,qBAAe,GAAG,IAAI;IACxB;EACF,CAAC;AACD,SAAO;AACT;;;ACjBM,SAAU,uBAAoB;AAClC,QAAM,aAA4C,CAAA;AAElD,aAAW,OAAO,IAAI;AACpB,eAAW,GAAG,GAAsB,CAAC,IACnC,OAAO,GAAG,GAAsB,CAAC;EACrC;AAEA,aAAW,OAAO,SAAS;AACzB,eAAW,QAAQ,GAA2B,CAAC,IAC7C,OAAO,QAAQ,GAA2B,CAAC;EAC/C;AAEA,aAAW,OAAO,gBAAgB;AAChC,eAAW,eAAe,GAAkC,CAAC,IAC3D,OAAO,eAAe,GAAkC,CAAC;EAC7D;AAEA,aAAW,OAAO,WAAW;AAC3B,eAAW,UAAU,GAA6B,CAAC,IACjD,OAAO,UAAU,GAA6B,CAAC;EACnD;AAEA,SAAO;AACT;;;ACpCA;;;;;;;;;;;;;;ACcM,SAAU,cACd,OACA,SACAC,UAAiB;AAEjB,UAAQA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,OAAO,QAAQ;AACjE;AAOO,IAAM,qBAAqB;;;ACb5B,SAAU,UACd,MACA,SACAC,UAAiB;AAEjB,UAAQA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,MAAM,GAAG;AAC3D;;;ACPM,SAAU,oBACd,OACAC,WAAmB,gBAAc;AAEjC,SAAOA,SAAQ,OAAO,OAAO,MAAM;AACrC;;;ACLM,SAAU,iBAAiB,YAAoBC,WAAU,gBAAc;AAC3E,MAAI,aAAa,IAAI;AACnB,WAAOA,SAAQ,aAAa,IAAI,WAAW,eAAc,CAAE,EAAE;EAC/D;AACA,SAAOA,SAAQ,aAAa,GAAG,WAAW,eAAc,CAAE,EAAE;AAC9D;;;ACVM,SAAU,yBAAsB;AACpC,SAAO;AACT;;;ACIM,SAAU,kBACd,SACA,SACAC,UAAiB;AAEjB,UAAQA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,SAAS,QAAQ;AACnE;;;ACRM,SAAU,mBACd,MACAC,WAAmB,gBAAc;AAEjC,SAAOA,SAAQ,OAAO,MAAM,MAAM;AACpC;AAOO,IAAM,oBAAoB;;;ACd3B,SAAU,cAAc,kBAA8C;AAC1E,MAAI,kBAAkB,sBAAsB,CAAC,iBAAiB,eAAe;AAC3E,qBAAiB,gBAAgB,iBAAiB;EACpD;AACA,MACE,kBAAkB,qBAClB,CAAC,iBAAiB,oBAClB;AACA,qBAAiB,qBAAqB,iBAAiB;EACzD;AACA,SAAO;IACL,GAAG;IACH,GAAG;;AAEP;;;ACLM,SAAU,gBACd,cACA,UACA,QACA,YACAC,UAAgB;AAEhB,QAAM,EACJ,cAAAC,eACA,aAAAC,cACA,WAAAC,YACA,qBAAAC,sBACA,UAAAC,UAAQ,IACNL;AAEJ,QAAM,SAASI,qBAAoB;IACjC,OAAOF,aAAY,YAAY;IAC/B,KAAKC,WAAU,YAAY;GAC5B;AAED,QAAM,UAAU,OAAO,IAAI,CAAC,UAAS;AACnC,UAAM,QAAQ,WAAW,oBAAoB,OAAOH,QAAO;AAC3D,UAAM,QAAQK,UAAS,KAAK;AAC5B,UAAM,WACH,YAAY,QAAQJ,cAAa,QAAQ,KACzC,UAAU,QAAQA,cAAa,MAAM,KACtC;AACF,WAAO,EAAE,OAAO,OAAO,SAAQ;EACjC,CAAC;AAED,SAAO;AACT;;;AClCM,SAAU,qBACd,cACA,SAA0B,CAAA,GAC1B,kBAA4C,CAAA,GAAE;AAE9C,MAAI,QAAuB,EAAE,GAAG,SAAS,GAAG,GAAG,EAAC;AAChD,SAAO,QAAQ,YAAY,EACxB,OAAO,CAAC,CAAC,EAAE,MAAM,MAAM,WAAW,IAAI,EACtC,QAAQ,CAAC,CAAC,QAAQ,MAAK;AACtB,YAAQ;MACN,GAAG;MACH,GAAG,kBAAkB,QAAQ;;EAEjC,CAAC;AACH,SAAO;AACT;;;ACnBM,SAAU,YACdK,UACA,SACA,mBAAuC;AAEvC,QAAM,QAAQA,SAAQ,MAAK;AAE3B,QAAM,QAAQ,oBACVA,SAAQ,qBAAqB,OAAOA,QAAO,IAC3C,UACEA,SAAQ,eAAe,KAAK,IAC5BA,SAAQ,YAAY,KAAK;AAE/B,QAAM,OAAe,CAAA;AACrB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,MAAMA,SAAQ,QAAQ,OAAO,CAAC;AACpC,SAAK,KAAK,GAAG;EACf;AACA,SAAO;AACT;;;ACdM,SAAU,eACd,UACA,QACA,YACAC,UAAgB;AAEhB,MAAI,CAAC;AAAU,WAAO;AACtB,MAAI,CAAC;AAAQ,WAAO;AACpB,QAAM,EAAE,aAAAC,cAAa,WAAAC,YAAW,UAAAC,WAAU,SAAAC,UAAS,UAAAC,WAAU,YAAAC,YAAU,IACrEN;AACF,QAAM,eAAeC,aAAY,QAAQ;AACzC,QAAM,cAAcC,WAAU,MAAM;AACpC,QAAM,QAAgB,CAAA;AAEtB,MAAI,OAAO;AACX,SAAOG,UAAS,MAAM,WAAW,KAAKC,YAAW,MAAM,WAAW,GAAG;AACnE,UAAM,KAAK,IAAI;AACf,WAAOH,UAAS,MAAM,CAAC;EACzB;AAEA,SAAO,MAAM,IAAI,CAACI,UAAQ;AACxB,UAAM,QAAQ,WAAW,mBAAmBA,OAAMP,QAAO;AACzD,WAAO;MACL,OAAOI,SAAQG,KAAI;MACnB;MACA,UAAU;;EAEd,CAAC;AACH;;;AC7CA;;;;;;;;;;;;;;;;;;ACcM,SAAU,UACd,MACA,SACAC,UAAiB;AAEjB,UAAQA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,MAAM,QAAQ;AAChE;AAMO,IAAM,eAAe;;;ACZtB,SAAU,cACd,MACA,WACA,SACAC,UAAiB;AAEjB,MAAI,SAASA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,MAAM,MAAM;AACjE,MAAI,WAAW,OAAO;AACpB,YAAQ,UAAU,KAAK;EACzB;AACA,SAAO;AACT;;;ACPM,SAAU,eACd,MACA,WACA,SACAC,UAAiB;AAEjB,MAAI,SAASA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,MAAM,MAAM;AACjE,MAAI,UAAU;AAAO,YAAQ,UAAU,KAAK;AAC5C,MAAI,UAAU;AAAU,YAAQ,GAAG,KAAK;AACxC,SAAO;AACT;AAMO,IAAM,WAAW;;;AC1BlB,SAAU,WAAQ;AACtB,SAAO;AACT;;;ACCM,SAAU,mBAAmB,SAAwB;AACzD,SAAO;AACT;;;ACHM,SAAU,UAAU,OAAuB;AAC/C,SAAO;AACT;;;ACFM,SAAU,cAAc,OAAuB;AACnD,SAAO;AACT;;;ACCM,SAAU,aACd,MACA,SACAC,UAAiB;AAEjB,UAAQA,YAAW,IAAI,QAAQ,OAAO,GAAG,OAAO,MAAM,MAAM;AAC9D;;;ACPM,SAAU,gBACd,YACA,SAAwB;AAExB,SAAO,QAAQ,UAAU;AAC3B;;;ACNM,SAAU,sBAAsB,SAAwB;AAC5D,SAAO;AACT;;;ACFM,SAAU,kBAAkB,SAAwB;AACxD,SAAO;AACT;;;ACbA,IAAAC,iBAA+C;AAQ/C,IAAM,gBAAgB,CAAC,YAA+C;AACpE,MAAI,mBAAmB;AAAa,WAAO;AAC3C,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,YAAyB;EAC9C,GAAI,QAAQ,iBAAiB,uBAAuB,KAAK,CAAA;;AAE3D,IAAM,eAAe,CAAC,YACpB,cAAc,QAAQ,cAAc,uBAAuB,CAAC;AAC9D,IAAM,iBAAiB,CAAC,YACtB,cAAc,QAAQ,cAAc,yBAAyB,CAAC;AAChE,IAAM,eAAe,CAAC,YACpB,cAAc,QAAQ,cAAc,uBAAuB,CAAC;AAC9D,IAAM,aAAa,CAAC,YAClB,cAAc,QAAQ,cAAc,qBAAqB,CAAC;AAC5D,IAAM,kBAAkB,CAAC,YACvB,cAAc,QAAQ,cAAc,0BAA0B,CAAC;AAa3D,SAAU,aACd,WACA,SACA,EACE,YACA,QACA,SACA,SAAAC,SAAO,GAMR;AAED,QAAM,gCAA4B,uBAAoB,IAAI;AAC1D,QAAM,wBAAoB,uBAAO,MAAM;AACvC,QAAM,mBAAe,uBAAO,KAAK;AAEjC,sCAAgB,MAAK;AAEnB,UAAM,iBAAiB,kBAAkB;AAEzC,sBAAkB,UAAU;AAE5B,QACE,CAAC,WACD,CAAC,UAAU;IAEX,EAAE,UAAU,mBAAmB;IAE/B,OAAO,WAAW,KAClB,eAAe,WAAW,KAC1B,OAAO,WAAW,eAAe,QACjC;AACA;IACF;AAEA,UAAMC,eAAcD,SAAQ,YAC1B,OAAO,CAAC,EAAE,MACV,eAAe,CAAC,EAAE,IAAI;AAGxB,UAAM,uBAAuBA,SAAQ,QACnC,OAAO,CAAC,EAAE,MACV,eAAe,CAAC,EAAE,IAAI;AAGxB,UAAM,wBAAwB,uBAC1B,WAAW,UAAU,mBAAmB,IACxC,WAAW,UAAU,oBAAoB;AAE7C,UAAM,sBAAsB,uBACxB,WAAW,UAAU,iBAAiB,IACtC,WAAW,UAAU,kBAAkB;AAG3C,UAAM,yBAAyB,0BAA0B;AAGzD,UAAM,iBAAiB,UAAU,QAAQ,UAAU,IAAI;AACvD,QAAI,0BAA0B,aAAa;AAGzC,YAAM,0BAA0B,cAAc,cAAc;AAC5D,8BAAwB,QAAQ,CAAC,2BAA0B;AACzD,YAAI,EAAE,kCAAkC;AAAc;AAGtD,cAAM,0BAA0B,aAAa,sBAAsB;AACnE,YACE,2BACA,uBAAuB,SAAS,uBAAuB,GACvD;AACA,iCAAuB,YAAY,uBAAuB;QAC5D;AAGA,cAAM,YAAY,eAAe,sBAAsB;AACvD,YAAI,WAAW;AACb,oBAAU,UAAU,OAAO,qBAAqB;QAClD;AAEA,cAAM,UAAU,aAAa,sBAAsB;AACnD,YAAI,SAAS;AACX,kBAAQ,UAAU,OAAO,mBAAmB;QAC9C;MACF,CAAC;AAED,gCAA0B,UAAU;IACtC,OAAO;AACL,gCAA0B,UAAU;IACtC;AAEA,QACE,aAAa,WACbC;IAEA,SACA;AACA;IACF;AAEA,UAAM,mBACJ,kCAAkC,cAC9B,cAAc,sBAAsB,IACpC,CAAA;AAEN,UAAM,kBAAkB,cAAc,UAAU,OAAO;AAEvD,QACE,mBACA,gBAAgB,MAAM,CAAC,OAAO,cAAc,WAAW,KACvD,oBACA,iBAAiB,MAAM,CAAC,OAAO,cAAc,WAAW,GACxD;AACA,mBAAa,UAAU;AACvB,YAAM,mBAAmC,CAAA;AAGzC,gBAAU,QAAQ,MAAM,YAAY;AAEpC,YAAM,QAAQ,WAAW,UAAU,OAAO;AAC1C,UAAI,OAAO;AACT,cAAM,MAAM,SAAS;MACvB;AAEA,sBAAgB,QAAQ,CAAC,gBAAgB,UAAS;AAChD,cAAM,kBAAkB,iBAAiB,KAAK;AAE9C,YAAI,CAAC,iBAAiB;AACpB;QACF;AAGA,uBAAe,MAAM,WAAW;AAChC,uBAAe,MAAM,WAAW;AAChC,cAAM,YAAY,eAAe,cAAc;AAC/C,YAAI,WAAW;AACb,oBAAU,UAAU,IAAI,qBAAqB;QAC/C;AAEA,cAAM,UAAU,aAAa,cAAc;AAC3C,YAAI,SAAS;AACX,kBAAQ,UAAU,IAAI,mBAAmB;QAC3C;AAGA,cAAM,UAAU,MAAK;AACnB,uBAAa,UAAU;AAEvB,cAAI,UAAU,SAAS;AACrB,sBAAU,QAAQ,MAAM,YAAY;UACtC;AACA,cAAI,OAAO;AACT,kBAAM,MAAM,SAAS;UACvB;AAEA,cAAI,WAAW;AACb,sBAAU,UAAU,OAAO,qBAAqB;UAClD;AACA,cAAI,SAAS;AACX,oBAAQ,UAAU,OAAO,mBAAmB;UAC9C;AACA,yBAAe,MAAM,WAAW;AAChC,yBAAe,MAAM,WAAW;AAChC,cAAI,eAAe,SAAS,eAAe,GAAG;AAC5C,2BAAe,YAAY,eAAe;UAC5C;QACF;AACA,yBAAiB,KAAK,OAAO;AAG7B,wBAAgB,MAAM,gBAAgB;AACtC,wBAAgB,MAAM,WAAW;AACjC,wBAAgB,MAAM,WAAW;AACjC,wBAAgB,aAAa,eAAe,MAAM;AAGlD,cAAM,qBAAqB,gBAAgB,eAAe;AAC1D,YAAI,oBAAoB;AACtB,6BAAmB,MAAM,UAAU;QACrC;AAEA,cAAM,oBAAoB,eAAe,eAAe;AACxD,YAAI,mBAAmB;AACrB,4BAAkB,UAAU,IAC1B,uBACI,WAAW,UAAU,mBAAmB,IACxC,WAAW,UAAU,kBAAkB,CAAC;AAE9C,4BAAkB,iBAAiB,gBAAgB,OAAO;QAC5D;AAEA,cAAM,kBAAkB,aAAa,eAAe;AACpD,YAAI,iBAAiB;AACnB,0BAAgB,UAAU,IACxB,uBACI,WAAW,UAAU,iBAAiB,IACtC,WAAW,UAAU,gBAAgB,CAAC;QAE9C;AAEA,uBAAe,aAAa,iBAAiB,eAAe,UAAU;MACxE,CAAC;IACH;EACF,CAAC;AACH;;;ACrPA,IAAAC,iBAA0B;;;ACepB,SAAU,SACd,eACA,SACA,OACAC,UAAgB;AAEhB,QAAM,aAAa,cAAc,CAAC;AAClC,QAAM,YAAY,cAAc,cAAc,SAAS,CAAC;AAExD,QAAM,EAAE,SAAS,YAAY,kBAAiB,IAAK,SAAS,CAAA;AAC5D,QAAM,EACJ,SAAAC,UACA,0BAAAC,2BACA,4BAAAC,6BACA,oBAAAC,qBACA,cAAAC,eACA,YAAAC,aACA,WAAAC,YACA,SAAAC,UACA,sBAAAC,uBACA,gBAAAC,iBACA,aAAAC,aAAW,IACTX;AAEJ,QAAM,qBAAqB,oBACvBS,sBAAqB,YAAYT,QAAO,IACxC,UACEU,gBAAe,UAAU,IACzBC,aAAY,UAAU;AAE5B,QAAM,kBAAkB,oBACpBP,oBAAmB,SAAS,IAC5B,UACEC,cAAaC,YAAW,SAAS,CAAC,IAClCC,WAAUD,YAAW,SAAS,CAAC;AAErC,QAAM,UAAUJ,0BAAyB,iBAAiB,kBAAkB;AAC5E,QAAM,YAAYC,4BAA2B,WAAW,UAAU,IAAI;AAEtE,QAAM,QAAgB,CAAA;AACtB,WAAS,IAAI,GAAG,KAAK,SAAS,KAAK;AACjC,UAAM,OAAOF,SAAQ,oBAAoB,CAAC;AAC1C,QAAI,WAAWO,SAAQ,MAAM,OAAO,GAAG;AACrC;IACF;AACA,UAAM,KAAK,IAAI;EACjB;AAGA,QAAM,yBAAyB,oBAAoB,KAAK;AACxD,QAAM,aAAa,yBAAyB;AAC5C,MAAI,cAAc,MAAM,SAAS,YAAY;AAC3C,UAAM,YAAY,aAAa,MAAM;AACrC,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,YAAM,OAAOP,SAAQ,MAAM,MAAM,SAAS,CAAC,GAAG,CAAC;AAC/C,YAAM,KAAK,IAAI;IACjB;EACF;AACA,SAAO;AACT;;;AChEM,SAAU,QAAQ,gBAA+B;AACrD,QAAM,cAA6B,CAAA;AACnC,SAAO,eAAe,OAAO,CAAC,MAAM,UAAS;AAC3C,UAAM,WAA0B,MAAM,MAAM,OAAO,CAACW,WAAU,SAAQ;AACpE,aAAO,CAAC,GAAGA,WAAU,GAAG,KAAK,IAAI;IACnC,GAAG,WAAW;AACd,WAAO,CAAC,GAAG,MAAM,GAAG,QAAQ;EAC9B,GAAG,WAAW;AAChB;;;ACLM,SAAU,iBACd,qBACA,kBACA,OACAC,UAAgB;AAEhB,QAAM,EAAE,iBAAiB,EAAC,IAAK;AAC/B,QAAM,SAAiB,CAAA;AACvB,WAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,UAAM,QAAQA,SAAQ,UAAU,qBAAqB,CAAC;AACtD,QAAI,oBAAoB,QAAQ,kBAAkB;AAChD;IACF;AACA,WAAO,KAAK,KAAK;EACnB;AACA,SAAO;AACT;;;ACfM,SAAU,gBACd,OAUA,UACA,QACAC,UAAgB;AAEhB,QAAM,EACJ,OACA,cACA,QAAQA,SAAQ,MAAK,GACrB,iBAAiB,EAAC,IAChB;AACJ,MAAI,eAAe,SAAS,gBAAgB;AAC5C,QAAM,EAAE,4BAAAC,6BAA4B,WAAAC,YAAW,cAAAC,cAAY,IAAKH;AAEhE,MACE,UACAC,4BAA2B,QAAQ,YAAY,IAAI,iBAAiB,GACpE;AACA,UAAM,SAAS,MAAM,iBAAiB;AACtC,mBAAeC,WAAU,QAAQ,MAAM;EACzC;AAEA,MAAI,YAAYD,4BAA2B,cAAc,QAAQ,IAAI,GAAG;AACtE,mBAAe;EACjB;AAEA,SAAOE,cAAa,YAAY;AAClC;;;ACjCM,SAAU,UACd,eACA,OACA,OAIAC,UAAgB;AAEhB,QAAM,EACJ,SAAAC,UACA,oBAAAC,qBACA,cAAAC,eACA,YAAAC,aACA,WAAAC,YACA,YAAAC,aACA,SAAAC,UACA,sBAAAC,uBACA,gBAAAC,iBACA,aAAAC,aAAW,IACTV;AAEJ,QAAM,kBAAkB,cAAc,OACpC,CAAC,QAAQ,UAAS;AAChB,UAAM,uBAAuB,MAAM,oBAC/BQ,sBAAqB,OAAOR,QAAO,IACnC,MAAM,UACJS,gBAAe,KAAK,IACpBC,aAAY,KAAK;AAEvB,UAAM,qBAAqB,MAAM,oBAC7BR,oBAAmB,KAAK,IACxB,MAAM,UACJC,cAAaC,YAAW,KAAK,CAAC,IAC9BC,WAAUD,YAAW,KAAK,CAAC;AAGjC,UAAM,aAAa,MAAM,OAAO,CAAC,SAAQ;AACvC,aAAO,QAAQ,wBAAwB,QAAQ;IACjD,CAAC;AAED,UAAM,yBAAyB,MAAM,oBAAoB,KAAK;AAE9D,QAAI,MAAM,cAAc,WAAW,SAAS,wBAAwB;AAClE,YAAM,aAAa,MAAM,OAAO,CAAC,SAAQ;AACvC,cAAM,YAAY,yBAAyB,WAAW;AACtD,eACE,OAAO,sBACP,QAAQH,SAAQ,oBAAoB,SAAS;MAEjD,CAAC;AACD,iBAAW,KAAK,GAAG,UAAU;IAC/B;AAEA,UAAM,QAAwB,WAAW,OACvC,CAACU,QAAO,SAAQ;AACd,YAAM,aAAa,MAAM,UAAUL,YAAW,IAAI,IAAIC,SAAQ,IAAI;AAClE,YAAM,OAAOI,OAAM,KAAK,CAACC,UAASA,MAAK,eAAe,UAAU;AAEhE,YAAM,MAAM,IAAI,YAAY,MAAM,OAAOZ,QAAO;AAChD,UAAI,CAAC,MAAM;AACT,QAAAW,OAAM,KAAK,IAAI,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC;MAChD,OAAO;AACL,aAAK,KAAK,KAAK,GAAG;MACpB;AACA,aAAOA;IACT,GACA,CAAA,CAAE;AAGJ,UAAM,iBAAiB,IAAI,cAAc,OAAO,KAAK;AACrD,WAAO,KAAK,cAAc;AAC1B,WAAO;EACT,GACA,CAAA,CAAE;AAGJ,MAAI,CAAC,MAAM,eAAe;AACxB,WAAO;EACT,OAAO;AACL,WAAO,gBAAgB,QAAO;EAChC;AACF;;;AC1FM,SAAU,aACd,OAaAE,UAAgB;AAEhB,MAAI,EAAE,YAAY,SAAQ,IAAK;AAE/B,QAAM,EACJ,aAAAC,cACA,YAAAC,aACA,cAAAC,eACA,YAAAC,aACA,UAAAC,WACA,WAAAC,YACA,SACA,MAAK,IACHN;AAGJ,QAAM,EAAE,UAAU,QAAQ,WAAW,QAAO,IAAK;AACjD,MAAI,CAAC,cAAc,WAAW;AAC5B,iBAAa;EACf;AACA,MAAI,CAAC,cAAc,UAAU;AAC3B,iBAAaA,SAAQ,QAAQ,UAAU,GAAG,CAAC;EAC7C;AACA,MAAI,CAAC,YAAY,SAAS;AACxB,eAAW;EACb;AACA,MAAI,CAAC,YAAY,QAAQ;AACvB,eAAW,QAAQ,QAAQ,IAAI,EAAE;EACnC;AAEA,QAAM,kBACJ,MAAM,kBAAkB,cACxB,MAAM,kBAAkB;AAC1B,MAAI,YAAY;AACd,iBAAaG,cAAa,UAAU;EACtC,WAAW,UAAU;AACnB,iBAAa,QAAQ,UAAU,GAAG,CAAC;EACrC,WAAW,CAAC,cAAc,iBAAiB;AACzC,iBAAaF,aAAYI,UAAS,MAAM,SAAS,MAAK,GAAI,IAAI,CAAC;EACjE;AACA,MAAI,UAAU;AACZ,eAAWD,YAAW,QAAQ;EAChC,WAAW,QAAQ;AACjB,eAAW,QAAQ,QAAQ,IAAI,EAAE;EACnC,WAAW,CAAC,YAAY,iBAAiB;AACvC,eAAWE,WAAU,MAAM,SAAS,MAAK,CAAE;EAC7C;AACA,SAAO;IACL,aAAaJ,YAAW,UAAU,IAAI;IACtC,WAAWA,YAAW,QAAQ,IAAI;;AAEtC;;;ACvDM,SAAU,aACd,qBACA,kBACA,SAIAK,UAAgB;AAEhB,MAAI,QAAQ,mBAAmB;AAC7B,WAAO;EACT;AACA,QAAM,EAAE,iBAAiB,iBAAiB,EAAC,IAAK;AAChD,QAAM,EAAE,cAAAC,eAAc,WAAAC,YAAW,4BAAAC,4BAA0B,IAAKH;AAChE,QAAM,SAAS,kBAAkB,iBAAiB;AAClD,QAAM,QAAQC,cAAa,mBAAmB;AAE9C,MAAI,CAAC,kBAAkB;AACrB,WAAOC,WAAU,OAAO,MAAM;EAChC;AAEA,QAAM,aAAaC,4BACjB,kBACA,mBAAmB;AAGrB,MAAI,aAAa,gBAAgB;AAC/B,WAAO;EACT;AAEA,SAAOD,WAAU,OAAO,MAAM;AAChC;;;AC9BM,SAAU,iBACd,qBACA,oBACA,SAIAE,UAAgB;AAEhB,MAAI,QAAQ,mBAAmB;AAC7B,WAAO;EACT;AACA,QAAM,EAAE,iBAAiB,eAAc,IAAK;AAC5C,QAAM,EAAE,cAAAC,eAAc,WAAAC,YAAW,4BAAAC,4BAA0B,IAAKH;AAChE,QAAM,SAAS,kBAAmB,kBAAkB,IAAK;AACzD,QAAM,QAAQC,cAAa,mBAAmB;AAC9C,MAAI,CAAC,oBAAoB;AACvB,WAAOC,WAAU,OAAO,CAAC,MAAM;EACjC;AACA,QAAM,aAAaC,4BAA2B,OAAO,kBAAkB;AAEvE,MAAI,cAAc,GAAG;AACnB,WAAO;EACT;AAEA,SAAOD,WAAU,OAAO,CAAC,MAAM;AACjC;;;ACvCM,SAAU,SAAS,QAAuB;AAC9C,QAAM,eAA+B,CAAA;AACrC,SAAO,OAAO,OAAO,CAAC,OAAO,UAAS;AACpC,WAAO,CAAC,GAAG,OAAO,GAAG,MAAM,KAAK;EAClC,GAAG,YAAY;AACjB;;;ACbA,IAAAE,iBAAyB;AA0BnB,SAAU,mBACd,cACA,iBAA8B;AAE9B,QAAM,CAAC,mBAAmB,QAAQ,QAAI,yBAAS,YAAY;AAE3D,QAAM,QACJ,oBAAoB,SAAY,oBAAoB;AAEtD,SAAO,CAAC,OAAO,QAAQ;AACzB;;;AVwCM,SAAU,YACd,OAqBAC,UAAgB;AAEhB,QAAM,CAAC,UAAU,MAAM,IAAI,aAAa,OAAOA,QAAO;AAEtD,QAAM,EAAE,cAAAC,eAAc,YAAAC,YAAU,IAAKF;AACrC,QAAM,eAAe,gBAAgB,OAAO,UAAU,QAAQA,QAAO;AACrE,QAAM,CAAC,YAAY,aAAa,IAAI;IAClC;;IAEA,MAAM,QAAQ,eAAe;EAAS;AAGxC,gCAAU,MAAK;AACb,UAAM,kBAAkB,gBAAgB,OAAO,UAAU,QAAQA,QAAO;AACxE,kBAAc,eAAe;EAE/B,GAAG,CAAC,MAAM,QAAQ,CAAC;AAGnB,QAAM,gBAAgB,iBAAiB,YAAY,QAAQ,OAAOA,QAAO;AAGzE,QAAM,QAAQ,SACZ,eACA,MAAM,WAAWE,YAAW,MAAM,QAAQ,IAAI,QAC9C,OACAF,QAAO;AAIT,QAAM,SAAS,UAAU,eAAe,OAAO,OAAOA,QAAO;AAG7D,QAAM,QAAQ,SAAS,MAAM;AAG7B,QAAM,OAAO,QAAQ,MAAM;AAE3B,QAAM,gBAAgB,iBAAiB,YAAY,UAAU,OAAOA,QAAO;AAC3E,QAAM,YAAY,aAAa,YAAY,QAAQ,OAAOA,QAAO;AAEjE,QAAM,EAAE,mBAAmB,cAAa,IAAK;AAE7C,QAAM,kBAAkB,CAAC,QACvB,MAAM,KAAK,CAAC,SAAuB,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC;AAE5E,QAAM,YAAY,CAAC,SAAc;AAC/B,QAAI,mBAAmB;AACrB;IACF;AACA,QAAI,WAAWC,cAAa,IAAI;AAEhC,QAAI,YAAY,WAAWA,cAAa,QAAQ,GAAG;AACjD,iBAAWA,cAAa,QAAQ;IAClC;AAEA,QAAI,UAAU,WAAWA,cAAa,MAAM,GAAG;AAC7C,iBAAWA,cAAa,MAAM;IAChC;AACA,kBAAc,QAAQ;AACtB,oBAAgB,QAAQ;EAC1B;AAEA,QAAM,UAAU,CAAC,QAAoB;AAEnC,QAAI,gBAAgB,GAAG,GAAG;AACxB;IACF;AACA,cAAU,IAAI,IAAI;EACpB;AAEA,QAAM,WAAW;IACf;IACA;IACA;IAEA;IACA;IAEA;IACA;IAEA;IACA;;AAGF,SAAO;AACT;;;AWzLA,IAAAE,iBAAyB;;;ACIzB,IAAK;CAAL,SAAKC,sBAAmB;AACtB,EAAAA,qBAAAA,qBAAA,OAAA,IAAA,CAAA,IAAA;AACA,EAAAA,qBAAAA,qBAAA,UAAA,IAAA,CAAA,IAAA;AACA,EAAAA,qBAAAA,qBAAA,aAAA,IAAA,CAAA,IAAA;AACA,EAAAA,qBAAAA,qBAAA,iBAAA,IAAA,CAAA,IAAA;AACF,GALK,wBAAA,sBAAmB,CAAA,EAAA;AAgBxB,SAAS,eAAe,WAAoB;AAC1C,SACE,CAAC,UAAU,QAAQ,QAAQ,KAC3B,CAAC,UAAU,QAAQ,MAAM,KACzB,CAAC,UAAU,QAAQ,OAAO;AAE9B;AAgBM,SAAU,qBACd,MACA,cACA,YACA,aAAoC;AAEpC,MAAI;AAEJ,MAAI,2BAAqD;AACzD,aAAW,OAAO,MAAM;AACtB,UAAM,YAAY,aAAa,GAAG;AAElC,QAAI,eAAe,SAAS,GAAG;AAC7B,UACE,UAAU,QAAQ,OAAO,KACzB,2BAA2B,oBAAoB,iBAC/C;AACA,sBAAc;AACd,mCAA2B,oBAAoB;MACjD,WACE,aAAa,UAAU,GAAG,KAC1B,2BAA2B,oBAAoB,aAC/C;AACA,sBAAc;AACd,mCAA2B,oBAAoB;MACjD,WACE,WAAW,IAAI,IAAI,KACnB,2BAA2B,oBAAoB,UAC/C;AACA,sBAAc;AACd,mCAA2B,oBAAoB;MACjD,WACE,UAAU,QAAQ,KAAK,KACvB,2BAA2B,oBAAoB,OAC/C;AACA,sBAAc;AACd,mCAA2B,oBAAoB;MACjD;IACF;EACF;AAEA,MAAI,CAAC,aAAa;AAEhB,kBAAc,KAAK,KAAK,CAAC,QAAQ,eAAe,aAAa,GAAG,CAAC,CAAC;EACpE;AACA,SAAO;AACT;;;ACjEM,SAAU,iBACd,QACA,SACA,SACA,UACA,QACA,OACAC,UAAgB;AAEhB,QAAM,EAAE,SAAS,kBAAiB,IAAK;AACvC,QAAM,EACJ,SAAAC,UACA,WAAAC,YACA,UAAAC,WACA,UAAAC,WACA,oBAAAC,qBACA,cAAAC,eACA,WAAAC,YACA,KAAAC,MACA,KAAAC,MACA,sBAAAC,uBACA,gBAAAC,iBACA,aAAAC,aAAW,IACTZ;AACJ,QAAM,UAAU;IACd,KAAKC;IACL,MAAME;IACN,OAAOD;IACP,MAAME;IACN,aAAa,CAAC,SACZ,oBACIM,sBAAqB,MAAMV,QAAO,IAClC,UACEW,gBAAe,IAAI,IACnBC,aAAY,IAAI;IACxB,WAAW,CAAC,SACV,oBACIP,oBAAmB,IAAI,IACvB,UACEC,cAAa,IAAI,IACjBC,WAAU,IAAI;;AAGxB,MAAI,gBAAgB,QAAQ,MAAM,EAAE,SAAS,YAAY,UAAU,IAAI,EAAE;AACzE,MAAI,YAAY,YAAY,UAAU;AACpC,oBAAgBC,KAAI,CAAC,UAAU,aAAa,CAAC;EAC/C,WAAW,YAAY,WAAW,QAAQ;AACxC,oBAAgBC,KAAI,CAAC,QAAQ,aAAa,CAAC;EAC7C;AACA,SAAO;AACT;;;AC7CM,SAAU,aACd,QACA,SACA,QACA,oBACA,kBACA,OAIAI,UACA,UAAkB,GAAC;AAEnB,MAAI,UAAU,KAAK;AAEjB,WAAO;EACT;AAEA,QAAM,gBAAgB,iBACpB,QACA,SACA,OAAO,MACP,oBACA,kBACA,OACAA,QAAO;AAGT,QAAM,aAAa,QACjB,MAAM,YAAY,mBAAmB,eAAe,MAAM,UAAUA,QAAO,CAAC;AAG9E,QAAM,WAAW,QACf,MAAM,UAAU,mBAAmB,eAAe,MAAM,QAAQA,QAAO,CAAC;AAG1E,QAAM,cAAc;AACpB,QAAM,WAAW,IAAI,YAAY,eAAe,aAAaA,QAAO;AAEpE,MAAI,CAAC,cAAc,CAAC,UAAU;AAC5B,WAAO;EACT;AAGA,SAAO,aACL,QACA,SACA,UACA,oBACA,kBACA,OACAA,UACA,UAAU,CAAC;AAEf;;;AHtCM,SAAU,SACd,OACA,UACA,cACA,YACAC,UAAgB;AAEhB,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,CAAC,aAAa,cAAc,QAAI,yBAAQ;AAE9C,QAAM,cAAc,qBAClB,SAAS,MACT,cACA,eAAe,MAAM,QACrB,WAAW;AAEb,QAAM,CAAC,YAAY,UAAU,QAAI,yBAC/B,YAAY,cAAc,MAAS;AAGrC,QAAM,OAAO,MAAK;AAChB,mBAAe,UAAU;AACzB,eAAW,MAAS;EACtB;AAEA,QAAM,YAAY,CAAC,QAAqB,YAAyB;AAC/D,QAAI,CAAC;AAAY;AACjB,UAAM,YAAY,aAChB,QACA,SACA,YACA,SAAS,UACT,SAAS,QACT,OACAA,QAAO;AAET,QAAI,CAAC;AAAW;AAEhB,aAAS,QAAQ,SAAS;AAC1B,eAAW,SAAS;EACtB;AAEA,QAAM,gBAAgB,CAAC,QAAoB;AACzC,WAAO,QAAQ,aAAa,UAAU,GAAG,CAAC;EAC5C;AAEA,QAAMC,YAAqB;IACzB;IACA;IACA,SAAS;IACT;IACA;;AAGF,SAAOA;AACT;;;AI/EM,SAAU,SACd,OACAC,UAAgB;AAEhB,QAAM,EACJ,UAAU,mBACV,UACA,SAAQ,IACN;AAEJ,QAAM,CAAC,oBAAoB,WAAW,IAAI,mBACxC,mBACA,WAAW,oBAAoB,MAAS;AAG1C,QAAM,WAAW,CAAC,WAAW,qBAAqB;AAElD,QAAM,EAAE,WAAAC,WAAS,IAAKD;AAEtB,QAAM,aAAa,CAAC,SAAc;AAChC,WAAO,UAAU,KAAK,CAAC,MAAMC,WAAU,GAAG,IAAI,CAAC,KAAK;EACtD;AAEA,QAAM,EAAE,KAAAC,MAAK,KAAAC,KAAG,IAAK;AAErB,QAAM,SAAS,CACb,aACA,WACA,MACE;AACF,QAAI,WAA+B,CAAC,GAAI,YAAY,CAAA,CAAG;AACvD,QAAI,WAAW,WAAW,GAAG;AAC3B,UAAI,UAAU,WAAWD,MAAK;AAE5B;MACF;AACA,UAAI,YAAY,UAAU,WAAW,GAAG;AAEtC;MACF;AACA,iBAAW,UAAU,OAAO,CAAC,MAAM,CAACD,WAAU,GAAG,WAAW,CAAC;IAC/D,OAAO;AACL,UAAI,UAAU,WAAWE,MAAK;AAE5B,mBAAW,CAAC,WAAW;MACzB,OAAO;AAEL,mBAAW,CAAC,GAAG,UAAU,WAAW;MACtC;IACF;AACA,QAAI,CAAC,UAAU;AACb,kBAAY,QAAQ;IACtB;AACA,eAAW,UAAU,aAAa,WAAW,CAAC;AAC9C,WAAO;EACT;AAEA,SAAO;IACL;IACA;IACA;;AAEJ;;;AClEM,SAAU,WACd,MACA,cACAC,OAAM,GACNC,OAAM,GACN,WAAW,OACXC,WAAmB,gBAAc;AAEjC,QAAM,EAAE,MAAM,GAAE,IAAK,gBAAgB,CAAA;AACrC,QAAM,EAAE,WAAAC,YAAW,SAAAC,UAAS,UAAAC,UAAQ,IAAKH;AAEzC,MAAI;AAEJ,MAAI,CAAC,QAAQ,CAAC,IAAI;AAEhB,YAAQ,EAAE,MAAM,MAAM,IAAIF,OAAM,IAAI,SAAY,KAAI;EACtD,WAAW,QAAQ,CAAC,IAAI;AAEtB,QAAIG,WAAU,MAAM,IAAI,GAAG;AAEzB,UAAI,UAAU;AACZ,gBAAQ,EAAE,MAAM,IAAI,OAAS;MAC/B,OAAO;AACL,gBAAQ;MACV;IACF,WAAWE,UAAS,MAAM,IAAI,GAAG;AAE/B,cAAQ,EAAE,MAAM,MAAM,IAAI,KAAI;IAChC,OAAO;AAEL,cAAQ,EAAE,MAAM,IAAI,KAAI;IAC1B;EACF,WAAW,QAAQ,IAAI;AAErB,QAAIF,WAAU,MAAM,IAAI,KAAKA,WAAU,IAAI,IAAI,GAAG;AAEhD,UAAI,UAAU;AACZ,gBAAQ,EAAE,MAAM,GAAE;MACpB,OAAO;AACL,gBAAQ;MACV;IACF,WAAWA,WAAU,MAAM,IAAI,GAAG;AAEhC,cAAQ,EAAE,MAAM,IAAIH,OAAM,IAAI,SAAY,KAAI;IAChD,WAAWG,WAAU,IAAI,IAAI,GAAG;AAE9B,cAAQ,EAAE,MAAM,MAAM,IAAIH,OAAM,IAAI,SAAY,KAAI;IACtD,WAAWK,UAAS,MAAM,IAAI,GAAG;AAE/B,cAAQ,EAAE,MAAM,MAAM,GAAM;IAC9B,WAAWD,SAAQ,MAAM,IAAI,GAAG;AAE9B,cAAQ,EAAE,MAAM,IAAI,KAAI;IAC1B,WAAWA,SAAQ,MAAM,EAAE,GAAG;AAE5B,cAAQ,EAAE,MAAM,IAAI,KAAI;IAC1B,OAAO;AACL,YAAM,IAAI,MAAM,eAAe;IACjC;EACF;AAGA,MAAI,OAAO,QAAQ,OAAO,IAAI;AAC5B,UAAM,OAAOF,SAAQ,yBAAyB,MAAM,IAAI,MAAM,IAAI;AAClE,QAAID,OAAM,KAAK,OAAOA,MAAK;AACzB,cAAQ,EAAE,MAAM,MAAM,IAAI,OAAS;IACrC,WAAWD,OAAM,KAAK,OAAOA,MAAK;AAChC,cAAQ,EAAE,MAAM,MAAM,IAAI,OAAS;IACrC;EACF;AAEA,SAAO;AACT;;;AC1EM,SAAU,uBACd,OACA,WACAM,WAAmB,gBAAc;AAEjC,QAAM,eAAe,CAAC,MAAM,QAAQ,SAAS,IAAI,CAAC,SAAS,IAAI;AAC/D,MAAI,OAAO,MAAM;AACjB,QAAM,YAAYA,SAAQ,yBAAyB,MAAM,IAAI,MAAM,IAAI;AAGvE,QAAM,iBAAiB,KAAK,IAAI,WAAW,CAAC;AAC5C,WAAS,IAAI,GAAG,KAAK,gBAAgB,KAAK;AACxC,QAAI,aAAa,SAAS,KAAK,OAAM,CAAE,GAAG;AACxC,aAAO;IACT;AACA,WAAOA,SAAQ,QAAQ,MAAM,CAAC;EAChC;AACA,SAAO;AACT;;;AClBM,SAAU,cACd,WACA,YACAC,WAAU,gBAAc;AAExB,SACE,kBAAkB,WAAW,WAAW,MAAM,OAAOA,QAAO,KAC5D,kBAAkB,WAAW,WAAW,IAAI,OAAOA,QAAO,KAC1D,kBAAkB,YAAY,UAAU,MAAM,OAAOA,QAAO,KAC5D,kBAAkB,YAAY,UAAU,IAAI,OAAOA,QAAO;AAE9D;;;ACCM,SAAU,uBACd,OACA,WACAC,WAAmB,gBAAc;AAEjC,QAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAGlE,QAAM,sBAAsB,SAAS,OACnC,CAAC,YAAY,OAAO,YAAY,UAAU;AAG5C,QAAM,4BAA4B,oBAAoB,KAAK,CAAC,YAAW;AACrE,QAAI,OAAO,YAAY;AAAW,aAAO;AAEzC,QAAIA,SAAQ,OAAO,OAAO,GAAG;AAC3B,aAAO,kBAAkB,OAAO,SAAS,OAAOA,QAAO;IACzD;AAEA,QAAI,aAAa,SAASA,QAAO,GAAG;AAClC,aAAO,QAAQ,KAAK,CAAC,SACnB,kBAAkB,OAAO,MAAM,OAAOA,QAAO,CAAC;IAElD;AAEA,QAAI,YAAY,OAAO,GAAG;AACxB,UAAI,QAAQ,QAAQ,QAAQ,IAAI;AAC9B,eAAO,cACL,OACA,EAAE,MAAM,QAAQ,MAAM,IAAI,QAAQ,GAAE,GACpCA,QAAO;MAEX;AACA,aAAO;IACT;AAEA,QAAI,gBAAgB,OAAO,GAAG;AAC5B,aAAO,uBAAuB,OAAO,QAAQ,WAAWA,QAAO;IACjE;AAEA,QAAI,eAAe,OAAO,GAAG;AAC3B,YAAM,mBAAmBA,SAAQ,QAAQ,QAAQ,QAAQ,QAAQ,KAAK;AACtE,UAAI,kBAAkB;AACpB,eAAO,cACL,OACA;UACE,MAAMA,SAAQ,QAAQ,QAAQ,OAAO,CAAC;UACtC,IAAIA,SAAQ,QAAQ,QAAQ,QAAQ,EAAE;WAExCA,QAAO;MAEX;AACA,aACE,mBAAmB,MAAM,MAAM,SAASA,QAAO,KAC/C,mBAAmB,MAAM,IAAI,SAASA,QAAO;IAEjD;AAEA,QAAI,gBAAgB,OAAO,KAAK,iBAAiB,OAAO,GAAG;AACzD,aACE,mBAAmB,MAAM,MAAM,SAASA,QAAO,KAC/C,mBAAmB,MAAM,IAAI,SAASA,QAAO;IAEjD;AAEA,WAAO;EACT,CAAC;AAED,MAAI,2BAA2B;AAC7B,WAAO;EACT;AAEA,QAAM,mBAAmB,SAAS,OAChC,CAAC,YAAY,OAAO,YAAY,UAAU;AAG5C,MAAI,iBAAiB,QAAQ;AAC3B,QAAI,OAAO,MAAM;AACjB,UAAM,YAAYA,SAAQ,yBAAyB,MAAM,IAAI,MAAM,IAAI;AAEvE,aAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACnC,UAAI,iBAAiB,KAAK,CAAC,YAAY,QAAQ,IAAI,CAAC,GAAG;AACrD,eAAO;MACT;AACA,aAAOA,SAAQ,QAAQ,MAAM,CAAC;IAChC;EACF;AAEA,SAAO;AACT;;;AC7FM,SAAU,SACd,OACAC,UAAgB;AAEhB,QAAM,EACJ,UACA,iBACA,UAAU,mBACV,UACA,SAAQ,IACN;AAEJ,QAAM,CAAC,oBAAoB,WAAW,IAAI,mBACxC,mBACA,WAAW,oBAAoB,MAAS;AAG1C,QAAM,WAAW,CAAC,WAAW,qBAAqB;AAElD,QAAM,aAAa,CAAC,SAClB,YAAY,kBAAkB,UAAU,MAAM,OAAOA,QAAO;AAE9D,QAAM,SAAS,CACb,aACA,WACA,MACE;AACF,UAAM,EAAE,KAAAC,MAAK,KAAAC,KAAG,IAAK;AACrB,UAAM,WAAW,cACb,WAAW,aAAa,UAAUD,MAAKC,MAAK,UAAUF,QAAO,IAC7D;AAEJ,QAAI,mBAAmB,YAAY,UAAU,QAAQ,SAAS,IAAI;AAChE,UACE,uBACE,EAAE,MAAM,SAAS,MAAM,IAAI,SAAS,GAAE,GACtC,UACAA,QAAO,GAET;AAEA,iBAAS,OAAO;AAChB,iBAAS,KAAK;MAChB;IACF;AAEA,QAAI,CAAC,UAAU;AACb,kBAAY,QAAQ;IACtB;AACA,eAAW,UAAU,aAAa,WAAW,CAAC;AAE9C,WAAO;EACT;AAEA,SAAO;IACL;IACA;IACA;;AAEJ;;;ACrDM,SAAU,UACd,OACAG,UAAgB;AAEhB,QAAM,EACJ,UAAU,mBACV,UACA,SAAQ,IACN;AAEJ,QAAM,CAAC,oBAAoB,WAAW,IAAI,mBACxC,mBACA,WAAW,oBAAoB,MAAS;AAG1C,QAAM,WAAW,CAAC,WAAW,qBAAqB;AAElD,QAAM,EAAE,WAAAC,WAAS,IAAKD;AAEtB,QAAM,aAAa,CAAC,gBAAqB;AACvC,WAAO,WAAWC,WAAU,UAAU,WAAW,IAAI;EACvD;AAEA,QAAM,SAAS,CACb,aACA,WACA,MACE;AACF,QAAI,UAA4B;AAChC,QAAI,CAAC,YAAY,YAAY,YAAYA,WAAU,aAAa,QAAQ,GAAG;AAEzE,gBAAU;IACZ;AACA,QAAI,CAAC,UAAU;AACb,kBAAY,OAAO;IACrB;AACA,QAAI,UAAU;AACZ,iBAAW,SAAiB,aAAa,WAAW,CAAC;IACvD,OAAO;AACL,iBAAW,SAAS,aAAa,WAAW,CAAC;IAC/C;AACA,WAAO;EACT;AAEA,SAAO;IACL;IACA;IACA;;AAEJ;;;AC5DM,SAAU,aACd,OACAC,UAAgB;AAEhB,QAAM,SAAS,UAAU,OAAOA,QAAO;AACvC,QAAM,QAAQ,SAAS,OAAOA,QAAO;AACrC,QAAM,QAAQ,SAAS,OAAOA,QAAO;AAErC,UAAQ,MAAM,MAAM;IAClB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,aAAO;EACX;AACF;;;AjGQM,SAAU,UAAU,cAA4B;AACpD,MAAI,QAAQ;AAEZ,MAAI,MAAM,UAAU;AAClB,YAAQ;MACN,GAAG;;AAEL,QAAI,MAAM,OAAO;AACf,YAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,MAAM,QAAQ;IACtD;AACA,QAAI,MAAM,OAAO;AACf,YAAM,QAAQ,IAAI,OAAO,MAAM,OAAO,MAAM,QAAQ;IACtD;AACA,QAAI,MAAM,cAAc;AACtB,YAAM,eAAe,IAAI,OAAO,MAAM,cAAc,MAAM,QAAQ;IACpE;AACA,QAAI,MAAM,YAAY;AACpB,YAAM,aAAa,IAAI,OAAO,MAAM,YAAY,MAAM,QAAQ;IAChE;AACA,QAAI,MAAM,UAAU;AAClB,YAAM,WAAW,IAAI,OAAO,MAAM,UAAU,MAAM,QAAQ;IAC5D;AACA,QAAI,MAAM,SAAS,YAAY,MAAM,UAAU;AAC7C,YAAM,WAAW,IAAI,OAAO,MAAM,UAAU,MAAM,QAAQ;IAC5D,WAAW,MAAM,SAAS,cAAc,MAAM,UAAU;AACtD,YAAM,WAAW,MAAM,UAAU,IAC/B,CAAC,SAAS,IAAI,OAAO,MAAM,MAAM,QAAQ,CAAC;IAE9C,WAAW,MAAM,SAAS,WAAW,MAAM,UAAU;AACnD,YAAM,WAAW;QACf,MAAM,MAAM,SAAS,OACjB,IAAI,OAAO,MAAM,SAAS,MAAM,MAAM,QAAQ,IAC9C;QACJ,IAAI,MAAM,SAAS,KACf,IAAI,OAAO,MAAM,SAAS,IAAI,MAAM,QAAQ,IAC5C;;IAER;EACF;AACA,QAAM,EAAE,YAAY,YAAY,QAAQ,SAAAC,UAAS,QAAQ,WAAU,QACjE,wBAAQ,MAAK;AACX,UAAMC,UAAS,EAAE,GAAG,MAAe,GAAG,MAAM,OAAM;AAElD,UAAMD,WAAU,IAAI,QAClB;MACE,QAAAC;MACA,cAAc,MAAM,oBAAoB,IAAI,MAAM;MAClD,uBAAuB,MAAM;MAC7B,6BAA6B,MAAM;MACnC,8BAA8B,MAAM;MACpC,UAAU,MAAM;MAChB,UAAU,MAAM;OAElB,MAAM,OAAO;AAGf,WAAO;MACL,SAAAD;MACA,YAAY,cAAc,MAAM,UAAU;MAC1C,YAAY,cAAc,MAAM,UAAU;MAC1C,QAAQ,EAAE,GAAG,gBAAe,GAAG,MAAM,OAAM;MAC3C,QAAAC;MACA,YAAY,EAAE,GAAG,qBAAoB,GAAI,GAAG,MAAM,WAAU;;EAEhE,GAAG;IACD,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;GACP;AAEH,QAAM,EACJ,eACA,MACA,WACA,iBAAiB,GACjB,WACA,YACA,YACA,cACA,iBACA,iBACA,aACA,aACA,gBACA,OAAM,IACJ;AAEJ,QAAM,EACJ,eAAAC,gBACA,WAAAC,YACA,qBAAAC,sBACA,kBAAAC,mBACA,wBAAAC,yBACA,mBAAAC,oBACA,oBAAAC,oBAAkB,IAChB;AAEJ,QAAM,WAAW,YAAY,OAAOR,QAAO;AAE3C,QAAM,EACJ,MACA,QACA,UACA,QACA,eACA,WACA,UAAS,IACP;AAEJ,QAAM,eAAe,mBACnB,MACA,OACA,UACA,QACAA,QAAO;AAGT,QAAM,EACJ,YACA,QACA,UAAU,cAAa,IACrB,aAAa,OAAOA,QAAO,KAAK,CAAA;AAEpC,QAAM,EAAE,MAAM,SAAS,eAAe,WAAW,WAAU,IAAK,SAC9D,OACA,UACA,cACA,eAAe,MAAM,QACrBA,QAAO;AAGT,QAAM,EACJ,gBAAAS,iBACA,eAAAC,gBACA,WAAAC,YACA,oBAAAC,qBACA,UAAAC,WACA,eAAAC,gBACA,WAAAC,YACA,cAAAC,eACA,iBAAAC,kBACA,uBAAAC,wBACA,mBAAAC,mBAAiB,IACf;AAEJ,QAAM,eAAW,wBACf,MAAM,YAAYnB,UAAS,MAAM,OAAO,GACxC,CAACA,UAAS,MAAM,OAAO,CAAC;AAG1B,QAAM,gBAAgB,SAAS,UAAa,eAAe;AAE3D,QAAM,0BAAsB,4BAAY,MAAK;AAC3C,QAAI,CAAC;AAAe;AACpB,cAAU,aAAa;AACvB,kBAAc,aAAa;EAC7B,GAAG,CAAC,eAAe,WAAW,WAAW,CAAC;AAE1C,QAAM,sBAAkB,4BAAY,MAAK;AACvC,QAAI,CAAC;AAAW;AAChB,cAAU,SAAS;AACnB,kBAAc,SAAS;EACzB,GAAG,CAAC,WAAW,WAAW,WAAW,CAAC;AAEtC,QAAM,qBAAiB,4BACrB,CAAC,KAAkB,MAAiB,CAAC,MAAiB;AACpD,MAAE,eAAc;AAChB,MAAE,gBAAe;AACjB,eAAW,GAAG;AACd,aAAS,IAAI,MAAM,GAAG,CAAC;AACvB,iBAAa,IAAI,MAAM,GAAG,CAAC;EAC7B,GACA,CAAC,QAAQ,YAAY,UAAU,CAAC;AAGlC,QAAM,qBAAiB,4BACrB,CAAC,KAAkB,MAAiB,CAAC,MAAiB;AACpD,eAAW,GAAG;AACd,iBAAa,IAAI,MAAM,GAAG,CAAC;EAC7B,GACA,CAAC,YAAY,UAAU,CAAC;AAG1B,QAAM,oBAAgB,4BACpB,CAAC,KAAkB,MAAiB,CAAC,MAAiB;AACpD,SAAI;AACJ,gBAAY,IAAI,MAAM,GAAG,CAAC;EAC5B,GACA,CAAC,MAAM,SAAS,CAAC;AAGnB,QAAM,uBAAmB,4BACvB,CAAC,KAAkB,cAAyB,CAAC,MAAoB;AAC/D,UAAM,SAAsD;MAC1D,WAAW;QACT,EAAE,WAAW,UAAU;QACvB,MAAM,QAAQ,QAAQ,UAAU;;MAElC,YAAY;QACV,EAAE,WAAW,UAAU;QACvB,MAAM,QAAQ,QAAQ,WAAW;;MAEnC,WAAW,CAAC,EAAE,WAAW,SAAS,QAAQ,OAAO;MACjD,SAAS,CAAC,EAAE,WAAW,SAAS,QAAQ,QAAQ;MAChD,QAAQ,CAAC,EAAE,WAAW,SAAS,SAAS,QAAQ;MAChD,UAAU,CAAC,EAAE,WAAW,SAAS,SAAS,OAAO;MACjD,MAAM,CAAC,eAAe,QAAQ;MAC9B,KAAK,CAAC,aAAa,OAAO;;AAE5B,QAAI,OAAO,EAAE,GAAG,GAAG;AACjB,QAAE,eAAc;AAChB,QAAE,gBAAe;AACjB,YAAM,CAAC,QAAQ,OAAO,IAAI,OAAO,EAAE,GAAG;AACtC,gBAAU,QAAQ,OAAO;IAC3B;AACA,mBAAe,IAAI,MAAM,WAAW,CAAC;EACvC,GACA,CAAC,WAAW,cAAc,MAAM,GAAG,CAAC;AAGtC,QAAM,0BAAsB,4BAC1B,CAAC,KAAkB,cAAyB,CAAC,MAAiB;AAC5D,sBAAkB,IAAI,MAAM,WAAW,CAAC;EAC1C,GACA,CAAC,eAAe,CAAC;AAGnB,QAAM,0BAAsB,4BAC1B,CAAC,KAAkB,cAAyB,CAAC,MAAiB;AAC5D,sBAAkB,IAAI,MAAM,WAAW,CAAC;EAC1C,GACA,CAAC,eAAe,CAAC;AAGnB,QAAM,wBAAoB,4BACxB,CAAC,SAAe,CAAC,MAAqC;AACpD,UAAM,gBAAgB,OAAO,EAAE,OAAO,KAAK;AAC3C,UAAM,QAAQA,SAAQ,SAASA,SAAQ,aAAa,IAAI,GAAG,aAAa;AACxE,cAAU,KAAK;EACjB,GACA,CAACA,UAAS,SAAS,CAAC;AAGtB,QAAM,uBAAmB,4BACvB,CAAC,SAAe,CAAC,MAAqC;AACpD,UAAM,eAAe,OAAO,EAAE,OAAO,KAAK;AAC1C,UAAM,QAAQA,SAAQ,QAAQA,SAAQ,aAAa,IAAI,GAAG,YAAY;AACtE,cAAU,KAAK;EACjB,GACA,CAACA,UAAS,SAAS,CAAC;AAGtB,QAAM,EAAE,WAAW,MAAK,QAAK,wBAC3B,OAAO;IACL,WAAW,CAAC,WAAW,GAAG,IAAI,GAAG,MAAM,SAAS,EAC7C,OAAO,OAAO,EACd,KAAK,GAAG;IACX,OAAO,EAAE,GAAG,SAAS,GAAG,IAAI,GAAG,GAAG,MAAM,MAAK;MAE/C,CAAC,YAAY,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC;AAGpD,QAAM,iBAAiB,kBAAkB,KAAK;AAE9C,QAAM,gBAAY,uBAAuB,IAAI;AAC7C,eAAa,WAAW,QAAQ,MAAM,OAAO,GAAG;IAC9C;IACA;IACA;IACA,SAAAA;GACD;AAED,QAAM,eAAiD;IACrD,gBAAgB;IAChB,UAAU;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAGF,SACE,eAAAoB,QAAA;IAAC,iBAAiB;IAAQ,EAAC,OAAO,aAAY;IAC5C,eAAAA,QAAA;MAAC,WAAW;MAAI,EACd,SAAS,MAAM,UAAU,YAAY,QACrC,WACA,OACA,KAAK,MAAM,KACX,IAAI,MAAM,IACV,MAAM,MAAM,MACZ,OAAO,MAAM,OACb,OAAO,MAAM,OACb,MAAM,MAAM,MAAI,cACJ,MAAM,YAAY,GAAC,GAC3B,eAAc;MAElB,eAAAA,QAAA;QAAC,WAAW;QAAM,EAChB,WAAW,WAAW,GAAG,MAAM,GAC/B,OAAO,SAAS,GAAG,MAAM,EAAC;QAEzB,CAAC,MAAM,kBAAkB,CAAC,aACzB,eAAAA,QAAA,cAAC,WAAW,KAAG,EAAA,qBACM,MAAM,UAAU,SAAS,QAC5C,WAAW,WAAW,GAAG,GAAG,GAC5B,OAAO,SAAS,GAAG,GAAG,GAAC,cACXP,UAAQ,GACpB,iBAAiB,qBACjB,aAAa,iBACb,eACA,UAAoB,CAAA;QAGvB,OAAO,IAAI,CAAC,eAAe,iBAAgB;AAC1C,gBAAM,iBAAiB,gBACrB,cAAc,MACd,UACA,QACA,YACAb,QAAO;AAGT,gBAAM,gBAAgB,eACpB,UACA,QACA,YACAA,QAAO;AAET,iBACE,eAAAoB,QAAA;YAAC,WAAW;YAAK,EAAA,uBACM,MAAM,UAAU,SAAS,QAC9C,WAAW,WAAW,GAAG,KAAK,GAC9B,OAAO,SAAS,GAAG,KAAK,GACxB,KAAK,cACL,cACA,cAA4B;YAE3B,cAAc,YACb,CAAC,MAAM,kBACP,iBAAiB,KACf,eAAAA,QAAA;cAAC,WAAW;cAAmB,EAC7B,MAAK,UACL,WAAW,WAAW,GAAG,mBAAmB,GAC5C,UAAU,gBAAgB,SAAY,IAAE,iBACzB,gBAAgB,SAAY,MAAI,cACnCN,eAAc,aAAa,GACvC,SAAS,qBAAmB,wBACN,MAAM,UAAU,SAAS,OAAS;cAExD,eAAAM,QAAA,cAAC,WAAW,SAAO,EACjB,UAAU,gBAAgB,SAAY,MACtC,WAAW,WAAW,GAAG,OAAO,GAChC,aAAa,MAAM,QAAQ,QAAQ,UAAU,OAAM,CAAA;YACnD;YAGR,eAAAA,QAAA,cAAC,WAAW,cAAY,EAAA,yBACC,MAAM,UAAU,SAAS,QAChD,WAAW,WAAW,GAAG,YAAY,GACrC,OAAO,SAAS,GAAG,YAAY,GAC/B,eACA,aAA0B,GAEzB,eAAe,WAAW,UAAU,IACnC,eAAAA,QAAA;cAAC,WAAW;cAAW,EACrB,WAAW,WAAW,GAAG,SAAS,GAClC,OAAO,SAAS,GAAG,SAAS,EAAC;cAE5B,kBAAkB,cACnB,kBAAkB,oBAChB,eAAAA,QAAA,cAAC,WAAW,gBAAc,EACxB,WAAW,WAAW,GAAG,cAAc,GAAC,cAC5BR,oBAAkB,GAC9B,YACA,YACA,UAAU,QAAQ,MAAM,iBAAiB,GACzC,UAAU,kBAAkB,cAAc,IAAI,GAC9C,SAAS,gBACT,OAAO,SAAS,GAAG,QAAQ,GAC3B,OAAOZ,SAAQ,SAAS,cAAc,IAAI,EAAC,CAAA,IAG7C,eAAAoB,QAAA,cAAA,QAAA,MACGhB,qBAAoB,cAAc,MAAMJ,QAAO,CAAC;cAGpD,kBAAkB,cACnB,kBAAkB,mBAChB,eAAAoB,QAAA,cAAC,WAAW,eAAa,EACvB,WAAW,WAAW,GAAG,aAAa,GAAC,cAC3BD,mBAAkBnB,SAAQ,OAAO,GAC7C,YACA,YACA,UAAU,QAAQ,MAAM,iBAAiB,GACzC,UAAU,iBAAiB,cAAc,IAAI,GAC7C,SAAS,eACT,OAAO,SAAS,GAAG,QAAQ,GAC3B,OAAOA,SAAQ,QAAQ,cAAc,IAAI,EAAC,CAAA,IAG5C,eAAAoB,QAAA,cAAA,QAAA,MACGZ,oBAAmB,cAAc,MAAMR,QAAO,CAAC;cAGpD,eAAAoB,QAAA,cAAA,QAAA,EACE,MAAK,UAAQ,aACH,UACV,OAAO;gBACL,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACX,GAEAlB,eACC,cAAc,MACdF,SAAQ,SACRA,QAAO,CACR;YACI,IAGT,eAAAoB,QAAA,cAAC,WAAW,cAAY,EACtB,WAAW,WAAW,GAAG,YAAY,GACrC,MAAK,UAAQ,aACH,SAAQ,GAEjBlB,eACC,cAAc,MACdF,SAAQ,SACRA,QAAO,CACR,CAEJ;YAEF,cAAc,YACb,CAAC,MAAM,kBACP,iBAAiB,iBAAiB,KAChC,eAAAoB,QAAA;cAAC,WAAW;cAAe,EACzB,MAAK,UACL,WAAW,WAAW,GAAG,eAAe,GACxC,UAAU,YAAY,SAAY,IAAE,iBACrB,YAAY,SAAY,MAAI,cAC/BL,WAAU,SAAS,GAC/B,SAAS,iBAAe,wBACF,MAAM,UAAU,SAAS,OAAS;cAExD,eAAAK,QAAA,cAAC,WAAW,SAAO,EACjB,UAAU,YAAY,SAAY,MAClC,WAAW,WAAW,GAAG,OAAO,GAChC,aAAa,MAAM,QAAQ,QAAQ,SAAS,QAAO,CAAA;YACnD;YAGP,iBAAiB,iBAAiB,KACjC,cAAc,WACd,CAAC,MAAM,kBACL,eAAAA,QAAA,cAAC,WAAW,KAAG,EAAA,qBACM,MAAM,UAAU,SAAS,QAC5C,WAAW,WAAW,GAAG,GAAG,GAC5B,OAAO,SAAS,GAAG,GAAG,GAAC,cACXP,UAAQ,GACpB,iBAAiB,qBACjB,aAAa,iBACb,eACA,UAAoB,CAAA;YAI1B,eAAAO,QAAA;cAAC,WAAW;cAAS,EACnB,MAAK,QAAM,wBACW,SAAS,cAAc,SAAS,SAAO,cAE3DT,WAAU,cAAc,MAAMX,SAAQ,SAASA,QAAO,KACtD,QAEF,WAAW,WAAW,GAAG,SAAS,GAClC,OAAO,SAAS,GAAG,SAAS,EAAC;cAE5B,CAAC,MAAM,gBACN,eAAAoB,QAAA;gBAAC,WAAW;gBAAQ,EAAA,0BAEhB,MAAM,UAAU,SAAS,QAE3B,WAAW,WAAW,GAAG,QAAQ,GACjC,OAAO,SAAS,GAAG,QAAQ,EAAC;gBAE3B,kBACC,eAAAA,QAAA,cAAC,WAAW,kBAAgB,EAAA,cACdF,uBAAsBlB,SAAQ,OAAO,GACjD,WAAW,WAAW,GAAG,gBAAgB,GACzC,OAAO,SAAS,GAAG,gBAAgB,GACnC,OAAM,MAAK,GAEVM,wBAAsB,CAAE;gBAG5B,SAAS,IAAI,CAAC,SAAS,MACtB,eAAAc,QAAA,cAAC,WAAW,SAAO,EAAA,cACLJ,cACV,SACAhB,SAAQ,SACRA,QAAO,GAET,WAAW,WAAW,GAAG,OAAO,GAChC,KAAK,GACL,OAAO,SAAS,GAAG,OAAO,GAC1B,OAAM,MAAK,GAEVO,mBAAkB,SAASP,SAAQ,SAASA,QAAO,CAAC,CAExD;cAAC;cAGN,eAAAoB,QAAA,cAAC,WAAW,OAAK,EAAA,uBACM,MAAM,UAAU,SAAS,QAC9C,WAAW,WAAW,GAAG,KAAK,GAC9B,OAAO,SAAS,GAAG,KAAK,EAAC,GAExB,cAAc,MAAM,IAAI,CAAC,MAAM,cAAa;AAC3C,uBACE,eAAAA,QAAA;kBAAC,WAAW;kBAAI,EACd,WAAW,WAAW,GAAG,IAAI,GAC7B,KAAK,KAAK,YACV,OAAO,SAAS,GAAG,IAAI,GACvB,KAAU;kBAET,kBACC,eAAAA,QAAA,cAAC,WAAW,YAAU,EACpB,MACA,OAAO,SAAS,GAAG,UAAU,GAAC,cAClBH,iBAAgB,KAAK,YAAY;oBAC3C;mBACD,GACD,WAAW,WAAW,GAAG,UAAU,GACnC,OAAM,OACN,MAAK,YAAW,GAEfZ,kBAAiB,KAAK,YAAYL,QAAO,CAAC;kBAG9C,KAAK,KAAK,IAAI,CAAC,QAAoB;AAClC,0BAAM,EAAE,KAAI,IAAK;AACjB,0BAAM,YAAY,aAAa,GAAG;AAElC,8BAAU,QAAQ,OAAO,IACvB,CAAC,UAAU,UACX,QAAQ,SAAS,UAAU,GAAG,CAAC;AAEjC,8BAAU,eAAe,QAAQ,IAC/B,aAAa,IAAI,KAAK,UAAU;AAElC,wBAAI,YAAY,aAAa,GAAG;AAE9B,4BAAM,EAAE,MAAM,GAAE,IAAK;AACrB,gCAAU,eAAe,WAAW,IAAI,QACtC,QAAQ,MAAMA,SAAQ,UAAU,MAAM,IAAI,CAAC;AAE7C,gCAAU,eAAe,SAAS,IAAI,QACpC,QAAQ,MAAMA,SAAQ,UAAU,MAAM,EAAE,CAAC;AAE3C,gCAAU,eAAe,YAAY,IACnC,kBACE,eACA,MACA,MACAA,QAAO;oBAEb;AAEA,0BAAMqB,SAAQ,qBACZ,WACA,QACA,MAAM,eAAe;AAGvB,0BAAMC,aAAY,0BAChB,WACA,YACA,MAAM,mBAAmB;AAG3B,0BAAM,YACJ,CAAC,iBAAiB,CAAC,UAAU,SACzBZ,eACE,MACA,WACAV,SAAQ,SACRA,QAAO,IAET;AAEN,2BACE,eAAAoB,QAAA,cAAC,WAAW,KAAG,EACb,KAAK,GAAGpB,SAAQ,OAAO,MAAM,YAAY,CAAC,IAAIA,SAAQ,OAAO,IAAI,cAAc,SAAS,CAAC,IACzF,KACA,WACA,WAAWsB,WAAU,KAAK,GAAG,GAC7B,OAAOD,QACP,MAAK,YAAU,iBACA,UAAU,YAAY,QAAS,cAClC,WAAS,YACXrB,SAAQ,OAAO,MAAM,YAAY,GAAC,cAE1C,IAAI,UACAA,SAAQ,OAAO,MAAM,SAAS,IAC9B,QAAS,iBAEA,UAAU,YAAY,QAAS,iBAC/B,UAAU,YAAY,QAAS,eACjC,UAAU,UAAU,QAAS,gBAC5B,IAAI,WAAW,QAAS,gBACxB,UAAU,WAAW,QAAS,cAChC,UAAU,SAAS,OAAS,GAEvC,CAAC,UAAU,UAAU,gBACpB,eAAAoB,QAAA,cAAC,WAAW,WAAS,EACnB,WAAW,WAAW,GAAG,SAAS,GAClC,OAAO,SAAS,GAAG,SAAS,GAC5B,MAAK,UACL,KACA,WACA,UAAU,UAAU,YAAY,QAChC,UAAU,cAAc,GAAG,IAAI,IAAI,IAAE,cACzBX,gBACV,MACA,WACAT,SAAQ,SACRA,QAAO,GAET,SAAS,eAAe,KAAK,SAAS,GACtC,QAAQ,cAAc,KAAK,SAAS,GACpC,SAAS,eAAe,KAAK,SAAS,GACtC,WAAW,iBAAiB,KAAK,SAAS,GAC1C,cAAc,oBACZ,KACA,SAAS,GAEX,cAAc,oBACZ,KACA,SAAS,EACV,GAEAG,WAAU,MAAMH,SAAQ,SAASA,QAAO,CAAC,IAG5C,CAAC,UAAU,UACXG,WAAU,IAAI,MAAMH,SAAQ,SAASA,QAAO,CAC7C;kBAGP,CAAC;gBAAC;cAGR,CAAC,CAAC;YACe;UACE;QAG7B,CAAC;MAAC;MAEH,MAAM,UACL,eAAAoB,QAAA,cAAC,WAAW,QAAM,EAChB,WAAW,WAAW,GAAG,MAAM,GAC/B,OAAO,SAAS,GAAG,MAAM,GACzB,MAAK,UAAQ,aACH,SAAQ,GAEjB,MAAM,MAAM;IAEhB;EACe;AAGxB;;;AkGhsBO,IAAM,UAAU;AAsBhB,IAAM,MAAM;AA0FZ,IAAM,gBAAgB;", "names": ["import_react", "format", "UI", "DayFlag", "SelectionState", "Animation", "dateLib", "dateLib", "dateLib", "options", "dateLib", "dateLib", "dateLib", "differenceInCalendarDays", "isSameDay", "dateLib", "dateLib", "isSameDay", "differenceInCalendarDays", "isAfter", "dateLib", "isSameDay", "isSameMonth", "startOfMonth", "isBefore", "endOfMonth", "isAfter", "isMatch", "days", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "import_react", "React", "import_react", "labelPrevious", "labelNext", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "dateLib", "dateLib", "dateLib", "dateLib", "dateLib", "dateLib", "dateLib", "startOfMonth", "startOfYear", "endOfYear", "eachMonthOfInterval", "getMonth", "dateLib", "dateLib", "startOfYear", "endOfYear", "addYears", "getYear", "isBefore", "isSameYear", "year", "dateLib", "dateLib", "dateLib", "dateLib", "import_react", "dateLib", "isSameMonth", "import_react", "dateLib", "addDays", "differenceInCalendarDays", "differenceInCalendarMonths", "endOfBroadcastWeek", "endOfISOWeek", "endOfMonth", "endOfWeek", "isAfter", "startOfBroadcastWeek", "startOfISOWeek", "startOfWeek", "weekDays", "dateLib", "dateLib", "differenceInCalendarMonths", "addMonths", "startOfMonth", "dateLib", "addDays", "endOfBroadcastWeek", "endOfISOWeek", "endOfMonth", "endOfWeek", "getISOWeek", "getWeek", "startOfBroadcastWeek", "startOfISOWeek", "startOfWeek", "weeks", "week", "dateLib", "startOfYear", "startOfDay", "startOfMonth", "endOfMonth", "addYears", "endOfYear", "dateLib", "startOfMonth", "addMonths", "differenceInCalendarMonths", "dateLib", "startOfMonth", "addMonths", "differenceInCalendarMonths", "import_react", "dateLib", "startOfMonth", "endOfMonth", "import_react", "FocusTargetPriority", "dateLib", "addDays", "addMonths", "addWeeks", "addYears", "endOfBroadcastWeek", "endOfISOWeek", "endOfWeek", "max", "min", "startOfBroadcastWeek", "startOfISOWeek", "startOfWeek", "dateLib", "dateLib", "useFocus", "dateLib", "isSameDay", "min", "max", "min", "max", "dateLib", "isSameDay", "isAfter", "isBefore", "dateLib", "dateLib", "dateLib", "dateLib", "min", "max", "dateLib", "isSameDay", "dateLib", "dateLib", "locale", "formatCaption", "formatDay", "formatMonthDropdown", "formatWeekNumber", "formatWeekNumberHeader", "formatWeekdayName", "formatYearDropdown", "labelDayButton", "labelGridcell", "labelGrid", "labelMonthDropdown", "labelNav", "labelPrevious", "labelNext", "labelWeekday", "labelWeekNumber", "labelWeekNumberHeader", "labelYearDropdown", "React", "style", "className"]}