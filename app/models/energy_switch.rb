class EnergySwitch < ApplicationRecord
  enum status: [ :draft, :confirmed, :submitted_to_supplier, :switched, :rejected_by_supplier ]

  belongs_to :switch_user
  belongs_to :gas_energy_tariff, class_name: "UserTariff", optional: true
  belongs_to :electricity_energy_tariff, class_name: "UserTariff", optional: true
  belongs_to :address
  belongs_to :payment_method, optional: true
  belongs_to :switching_to_tariff, class_name: "EnergyTariff", optional: true
  has_many :supplier_submissions, dependent: :destroy

  # Scopes
  scope :with_matching_meters, ->(switch_user:, gas_msn:, electricity_msn:) do
    joins('LEFT JOIN user_tariffs gas_tariff ON gas_tariff.id = energy_switches.gas_energy_tariff_id')
    .joins('LEFT JOIN user_tariffs electricity_tariff ON electricity_tariff.id = energy_switches.electricity_energy_tariff_id')
    .where(switch_user: switch_user, status: :draft)
    .where('gas_tariff.meter_serial_number = :gas_msn OR electricity_tariff.meter_serial_number = :electricity_msn',
          gas_msn: gas_msn,
          electricity_msn: electricity_msn)
  end

  before_save :ensure_unique_reference_number

  validate :switching_to_tariff_present_unless_draft
  validates :reference_number, uniqueness: true, allow_nil: false
  validate :energy_tariff_required_unless_draft

  def available_tariffs
    fuel_types = []
    fuel_types << 'gas' if gas_energy_tariff.present?
    fuel_types << 'electricity' if electricity_energy_tariff.present?

    return [] if fuel_types.empty?

    # Cache the expensive tariff calculation
    cache_key = "available_tariffs_#{id}_#{fuel_types.sort.join('_')}_#{electricity_energy_tariff&.profile_class}_#{electricity_energy_tariff&.gsp_code}"

    Rails.cache.fetch(cache_key, expires_in: 30.minutes) do
      calculate_available_tariffs(fuel_types)
    end
  end

  def total_estimated_costs
    gas_costs = gas_energy_tariff.present? ? gas_energy_tariff.calculate_costs('gas') : [0, 0]
    electricity_costs = electricity_energy_tariff.present? ? electricity_energy_tariff.calculate_costs('electricity') : [0, 0]

    {
      gas: gas_costs,
      electricity: electricity_costs,
      total: [(gas_costs[0] + electricity_costs[0]).round(2), (gas_costs[1] + electricity_costs[1]).round(2)]
    }
  end

  private

  def calculate_available_tariffs(fuel_types)
    # Determine energy types to include based on user's current tariffs
    energy_type_condition = case fuel_types.size
                            when 2
                              # If user has both gas and electricity, display tariffs that support both
                              ['both']
                            when 1
                              # If user has only one fuel type, show tariffs for that type or 'both'
                              [fuel_types.first, 'both']
                            else
                              []
                            end

    # Build optimized query to get unique tariffs directly
    tariff_query = EnergyTariff.joins(:energy_tariff_rates, :supplier)
                               .where(energy_type: energy_type_condition)
                               .where(energy_tariff_rates: { fuel_type: fuel_types })
                               .distinct

    # Add electricity-specific conditions if needed
    if electricity_energy_tariff.present?
      profile_class = electricity_energy_tariff.profile_class
      gsp_code = electricity_energy_tariff.gsp_code

      tariff_query = tariff_query.where(
        energy_tariff_rates: {
          profile_class: profile_class,
          gsp_code: gsp_code
        }
      )
    end

    # Preload associations to avoid N+1 queries
    tariffs = tariff_query.includes(:supplier, energy_tariff_rates: [])

    # Preload all required energy tariff rates for cost calculations
    tariff_ids = tariffs.pluck(:id)
    preload_energy_tariff_rates(tariff_ids)

    # Format the tariffs with all needed information
    tariffs.map do |tariff|
      format_tariff_for_display(tariff, fuel_types)
    end
  end

  def current_stage
    if draft? || confirmed?
      1
    elsif submitted_to_supplier?
      2
    elsif supplier_processing?
      3
    elsif switched? || rejected_by_supplier?
      4
    end
  end

  def stages
    ["Submitted", "Processing", "Switching", "Complete"]
  end

  # Get a summary of submission history for admin/debugging
  def submission_history_summary
    supplier_submissions.order(created_at: :desc).map do |submission|
      {
        id: submission.id,
        supplier: submission.supplier.name,
        type: submission.submission_type,
        status: submission.status,
        submitted_at: submission.submitted_at&.strftime('%Y-%m-%d %H:%M:%S'),
        processed_at: submission.processed_at&.strftime('%Y-%m-%d %H:%M:%S'),
        attempt: submission.attempt_number,
        reference: submission.supplier_reference,
        error: submission.error_message,
        rejection: submission.rejection_reason
      }
    end
  end

  private

  def calculate_estimated_cost_optimized(tariff)
    return tariff.calculate_estimated_cost(gas_energy_tariff, electricity_energy_tariff) unless @preloaded_rates

    estimated_cost = {}
    tariff_rates = @preloaded_rates[tariff.id] || []

    # Calculate gas costs if applicable
    if gas_energy_tariff.present?
      gas_rate = tariff_rates.find { |rate| rate.fuel_type == 'gas' }
      if gas_rate
        annual_usage = gas_energy_tariff.gas_estimated_annual_usage.to_f
        unit_rate = gas_rate.unit_rate_inc_vat.to_f / 100
        standing_charge = gas_rate.standing_charge_inc_vat.to_f / 100
        yearly_cost = (unit_rate * annual_usage) + (standing_charge * 365)

        estimated_cost[:gas] = {
          monthly: (yearly_cost / 12).round(2),
          yearly: yearly_cost.round(2)
        }
      end
    end

    # Calculate electricity costs if applicable
    if electricity_energy_tariff.present?
      electricity_rate = tariff_rates.find { |rate| rate.fuel_type == 'electricity' }
      if electricity_rate
        annual_usage = electricity_energy_tariff.electricity_est_annual_usage.to_f
        profile_class = electricity_energy_tariff.profile_class.to_i

        if profile_class == 2 &&
           electricity_rate.day_unit_rate_inc_vat.present? &&
           electricity_rate.night_unit_rate_inc_vat.present?

          # Economy 7 calculation
          day_percent = 0.58
          night_percent = 0.42

          day_unit_rate = electricity_rate.day_unit_rate_inc_vat.to_f / 100
          night_unit_rate = electricity_rate.night_unit_rate_inc_vat.to_f / 100
          standing_charge = electricity_rate.standing_charge_inc_vat.to_f / 100

          day_usage = annual_usage * day_percent
          night_usage = annual_usage * night_percent

          yearly_cost = (day_unit_rate * day_usage) +
                        (night_unit_rate * night_usage) +
                        (standing_charge * 365)
        else
          # Standard meter calculation
          unit_rate = electricity_rate.unit_rate_inc_vat.to_f / 100
          standing_charge = electricity_rate.standing_charge_inc_vat.to_f / 100

          yearly_cost = (unit_rate * annual_usage) + (standing_charge * 365)
        end

        estimated_cost[:electricity] = {
          monthly: (yearly_cost / 12).round(2),
          yearly: yearly_cost.round(2)
        }
      end
    end

    # Calculate total costs
    if estimated_cost[:gas].present? && estimated_cost[:electricity].present?
      yearly_total = estimated_cost[:gas][:yearly] + estimated_cost[:electricity][:yearly]
      estimated_cost[:total] = {
        monthly: (yearly_total / 12).round(2),
        yearly: yearly_total.round(2)
      }
    end

    estimated_cost
  end

  def calculate_estimated_saving_optimized(tariff)
    return tariff.calculate_estimated_saving(gas_energy_tariff, electricity_energy_tariff) unless @preloaded_rates

    estimated_cost = calculate_estimated_cost_optimized(tariff)
    current_costs = total_estimated_costs

    return {} unless estimated_cost.present? && current_costs.present?

    savings = {}

    # Calculate savings for gas
    if estimated_cost[:gas].present? && current_costs[:gas].present?
      current_yearly = current_costs[:gas][0] # yearly cost is first element
      new_yearly = estimated_cost[:gas][:yearly]
      yearly_saving = current_yearly - new_yearly
      saving_percentage = current_yearly > 0 ? ((yearly_saving / current_yearly) * 100) : 0

      savings[:gas] = {
        amount: yearly_saving.round(1),
        percentage: saving_percentage.round(1)
      }
    end

    # Calculate savings for electricity
    if estimated_cost[:electricity].present? && current_costs[:electricity].present?
      current_yearly = current_costs[:electricity][0] # yearly cost is first element
      new_yearly = estimated_cost[:electricity][:yearly]
      yearly_saving = current_yearly - new_yearly
      saving_percentage = current_yearly > 0 ? ((yearly_saving / current_yearly) * 100) : 0

      savings[:electricity] = {
        amount: yearly_saving.round(1),
        percentage: saving_percentage.round(1)
      }
    end

    # Calculate total savings
    gas_saving_amount = savings[:gas]&.dig(:amount) || 0
    elec_saving_amount = savings[:electricity]&.dig(:amount) || 0
    total_saving_amount = gas_saving_amount + elec_saving_amount
    total_current_cost = current_costs[:total][0] # yearly cost is first element
    total_saving_percentage = total_current_cost > 0 ? ((total_saving_amount / total_current_cost) * 100) : 0

    savings[:total] = {
      amount: [total_saving_amount, 0].max.round(1),
      percentage: [total_saving_percentage, 0].max.round(1)
    }

    savings
  end

  def preload_energy_tariff_rates(tariff_ids)
    # Preload all energy tariff rates that will be needed for cost calculations
    conditions = []

    if electricity_energy_tariff.present?
      conditions << {
        energy_tariff_id: tariff_ids,
        fuel_type: 'electricity',
        gsp_code: electricity_energy_tariff.gsp_code,
        profile_class: electricity_energy_tariff.profile_class
      }
    end

    if gas_energy_tariff.present?
      gas_condition = {
        energy_tariff_id: tariff_ids,
        fuel_type: 'gas'
      }

      # Add gsp_code filter for gas if electricity tariff has one
      if electricity_energy_tariff&.gsp_code.present?
        gas_condition[:gsp_code] = electricity_energy_tariff.gsp_code
      end

      conditions << gas_condition
    end

    # Preload all required rates in a single query
    if conditions.any?
      query = EnergyTariffRate.none
      conditions.each do |condition|
        query = query.or(EnergyTariffRate.where(condition))
      end
      @preloaded_rates = query.to_a.group_by(&:energy_tariff_id)
    end
  end

  def switching_to_tariff_present_unless_draft
    if !draft? && switching_to_tariff.blank?
      errors.add(:switching_to_tariff, "must be present unless the status is draft")
    end
  end

  def energy_tariff_required_unless_draft
    if gas_energy_tariff.nil? && electricity_energy_tariff.nil?
      errors.add(:base, "At least one energy tariff (gas or electricity) must be present")
    end
  end

  def ensure_unique_reference_number
    return if reference_number.present?

    loop do
      self.reference_number = generate_reference_number
      break unless EnergySwitch.exists?(reference_number: reference_number)
    end
  end

  def generate_reference_number
    "ES#{Time.current.strftime('%Y%m%d')}#{SecureRandom.hex(3).upcase}"
  end

  def format_tariff_for_display(tariff, fuel_types)
    supplier = tariff.supplier

    # Use preloaded rates to avoid additional queries
    rates = tariff.energy_tariff_rates.select { |rate| fuel_types.include?(rate.fuel_type) }
    electricity_rate = rates.find { |rate| rate.fuel_type == 'electricity' }
    gas_rate = rates.find { |rate| rate.fuel_type == 'gas' }
    profile_class = electricity_energy_tariff&.profile_class

    tariff_info = {
      id: tariff.id,
      supplier_name: supplier.name,
      tariff_name: tariff.tariff_name,
      profile_class: profile_class,
      tariff_type: tariff.readable_tariff_type,
      exit_fee: tariff.exit_fees,
      estimated_cost: calculate_estimated_cost_optimized(tariff),
      estimated_saving: calculate_estimated_saving_optimized(tariff),
      payment_methods: tariff.payment_methods,
      tariff_ends: '-',
      price_guaranteed: '-',
      discounts: 'None',
      additional_charges: 'None',
      additional_services: 'None',
      logo: supplier.logo,
      trustpilot_rating: supplier.trustpilot_rating,
      about: supplier.about
    }

    # Set fuel type information
    set_fuel_type_info(tariff_info, electricity_rate, gas_rate)

    # Add rate details
    add_electricity_rate_details(tariff_info, electricity_rate) if electricity_rate.present?
    add_gas_rate_details(tariff_info, gas_rate) if gas_rate.present?

    tariff_info
  end
  
  def set_fuel_type_info(tariff_info, electricity_rate, gas_rate)
    if electricity_rate.present? && gas_rate.present?
      tariff_info[:fuel_types] = ['electricity', 'gas']
      tariff_info[:fuel_type] = 'Electricity & Gas'
    elsif electricity_rate.present?
      tariff_info[:fuel_types] = ['electricity']
      tariff_info[:fuel_type] = 'Electricity'
    elsif gas_rate.present?
      tariff_info[:fuel_types] = ['gas']
      tariff_info[:fuel_type] = 'Gas'
    end
  end
  
  def add_electricity_rate_details(tariff_info, electricity_rate)
    tariff_info[:electricity] = if electricity_rate.profile_class == 1
      {
        unit_rate: electricity_rate.unit_rate_inc_vat.to_f.round(2),
        standing_charge: electricity_rate.standing_charge_inc_vat.to_f.round(2)
      }
    elsif electricity_rate.profile_class == 2
      {
        standing_charge: electricity_rate.standing_charge_inc_vat.to_f.round(2),
        day_unit_rate: electricity_rate.day_unit_rate_inc_vat.to_f.round(2),
        night_unit_rate: electricity_rate.night_unit_rate_inc_vat.to_f.round(2),
        weekend_unit_rate: electricity_rate.weekend_unit_rate_inc_vat.to_f.round(2)
      }
    end
  end
  
  def add_gas_rate_details(tariff_info, gas_rate)
    tariff_info[:gas] = {
      unit_rate: gas_rate.unit_rate_inc_vat.to_f.round(2),
      standing_charge: gas_rate.standing_charge_inc_vat.to_f.round(2)
    }
  end
end
