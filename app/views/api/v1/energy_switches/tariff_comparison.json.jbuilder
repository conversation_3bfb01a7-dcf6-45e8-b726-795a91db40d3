json.extract! @energy_switch, :id, :status, :reference_number, :switching_to_tariff_id

json.available_tariffs @energy_switch.available_tariffs

json.switch_user do
  if @energy_switch.switch_user
    json.extract! @energy_switch.switch_user, :id, :first_name, :last_name, :email, :phone_number
    # Add other address attributes as needed
  else
    json.null!
  end
end

json.address do
  if @energy_switch.address
    json.extract! @energy_switch.address, :postcode, :posttown
    json.full_address @energy_switch.address.full_address.titleize
  else
    json.null!
  end
end

json.current_supplier do
  current_tariff = @energy_switch.gas_energy_tariff || @energy_switch.electricity_energy_tariff

  json.supplier_name current_tariff.supplier_name
  json.tariff_name current_tariff.tariff_name
  json.tariff_type current_tariff.tariff_type.titleize
  json.exit_fee current_tariff.exit_fees || 0
  json.payment_method current_tariff.payment_method
  json.profile_class current_tariff.profile_class
  json.tariff_ends '-'
  json.price_guaranteed '-'
  json.discounts 'None'
  json.additional_charges 'None'
  json.additional_services 'None'
  json.total_estimated_costs @energy_switch.total_estimated_costs

  json.fuel_type(
    if @energy_switch.gas_energy_tariff.present? && @energy_switch.electricity_energy_tariff.present?
      'Electricity & Gas'
    elsif @energy_switch.gas_energy_tariff.present?
      'Gas'
    elsif @energy_switch.electricity_energy_tariff.present?
      'Electricity'
    end
  )

  json.gas do
    if @energy_switch.gas_energy_tariff
      tariff = @energy_switch.gas_energy_tariff
      # yearly_cost = (tariff.gas_monthly_usage.to_f * (tariff.unit_rate.to_f / 100) * 12) + ((tariff.standing_charge.to_f / 100) * 365)
      json.extract! tariff, :meter_point_reference_number, :meter_point_administration_number, :meter_serial_number,
      :supplier_name, :payment_method
      json.unit_rate tariff.unit_rate.to_f.round(3)
      json.standing_charge tariff.standing_charge.to_f.round(3)
      json.estimated_annual_usage tariff.gas_estimated_annual_usage.to_f.round(2)
      json.monthly_usage tariff.gas_monthly_usage.to_f.round(2)
      json.tariff_type tariff.tariff_type.try(:titleize)
      # json.estimated_monthly_cost (yearly_cost / 12).round(2)
      # json.estimated_yearly_cost yearly_cost.round(2)
      json.formatted_estimated_cost @energy_switch.gas_energy_tariff&.formatted_estimated_cost('gas')
      json.estimated_costs @energy_switch.gas_energy_tariff&.calculate_costs('gas')
      json.mpan tariff.meter_point_administration_number
      json.meter_serial_number tariff.meter_serial_number
    else
      json.null!
    end
  end

  json.electricity do
    if @energy_switch.electricity_energy_tariff
      tariff = @energy_switch.electricity_energy_tariff
      # yearly_cost = (tariff.electricity_monthly_usage.to_f * (tariff.unit_rate.to_f / 100) * 12) + ((tariff.standing_charge.to_f / 100) * 365)
      json.extract! tariff, :meter_point_reference_number, :meter_point_administration_number, :meter_serial_number,
      :supplier_name, :payment_method
      json.unit_rate tariff.unit_rate.to_f.round(3)
      json.standing_charge tariff.standing_charge.to_f.round(3)
      json.estimated_annual_usage tariff.electricity_est_annual_usage.to_f.round(2)
      json.monthly_usage tariff.electricity_monthly_usage.to_f.round(2)
      json.tariff_type tariff.tariff_type.try(:titleize)
      # json.estimated_cost (yearly_cost / 12).round(2)
      # json.estimated_yearly_cost yearly_cost.round(2)
      json.formatted_estimated_cost @energy_switch.electricity_energy_tariff&.formatted_estimated_cost('electricity')
      json.estimated_costs @energy_switch.electricity_energy_tariff&.calculate_costs('electricity')
      json.mpan tariff.meter_point_administration_number
      json.meter_serial_number tariff.meter_serial_number
    else
      json.null!
    end
  end

  json.fuel_types(
    if @energy_switch.gas_energy_tariff.present? && @energy_switch.electricity_energy_tariff.present?
      ['electricity', 'gas']
    elsif @energy_switch.gas_energy_tariff.present?
      ['gas']
    elsif @energy_switch.electricity_energy_tariff.present?
      ['electricity']
    else
      []
    end
  )
end
