module Api
  module V1
    class EnergySwitchesController < Api::V1::ApiController
      before_action :set_energy_switch, only: [
        :tariff_comparison,
        :confirm_switch,
        :switch_status
      ]
      before_action :authorize_onboarding_for_switch, only: [
        :tariff_comparison,
        :confirm_switch,
        :switch_status
      ]

      def tariff_comparison
        # Prevent duplicate simultaneous requests
        request_key = "processing_#{@energy_switch.id}_#{request.uuid}"

        if Rails.cache.exist?(request_key)
          # Another request is already processing this, wait briefly
          sleep(0.1)
          render json: { error: "Request already processing" }, status: 429 and return
        end

        begin
          Rails.cache.write(request_key, true, expires_in: 30.seconds)
          @energy_switch.update(viewed_at: Time.zone.now)
          render :tariff_comparison
        ensure
          Rails.cache.delete(request_key)
        end
      end

      def confirm_switch
        permitted_params = confirm_switch_params
        # Convert to JSON and back to ensure native JSON format
        json_params = permitted_params.to_json
        hash_params = JSON.parse(json_params)
        ConfirmSwitchJob.perform_async(@energy_switch.id, hash_params)
        json_response({ message: "Switch confirmed", status: :confirmed }, :ok)
      end

      def switch_status
        set_profile_class_and_gsp_code_for_electricity_tariff(@energy_switch)

        electricity_tariff = @energy_switch.electricity_energy_tariff

        if electricity_tariff.present? && (electricity_tariff.gsp_code.blank? || electricity_tariff.profile_class.blank?)
          Rails.logger.error("Unexpected error: Electricity tariff is present but gsp_code or profile_class is blank")
          render json: { error: 'Unexpected error' }, status: :unprocessable_entity
        else
          switch_date = @energy_switch.switch_date

          render json: {
            currentStage: @energy_switch.current_stage,
            referenceNumber: @energy_switch.reference_number,
            supplier: @energy_switch.switching_to_tariff.try(:supplier).try(:name),
            switchDate: switch_date.present? ? switch_date.strftime("#{switch_date.day.ordinalize} %B %Y") : nil,
            isRejected: @energy_switch.rejected_by_supplier?,
            stages: @energy_switch.stages
          }
        end
      end

      private

      def set_energy_switch
        @energy_switch = EnergySwitch.includes(
          :switch_user,
          :address,
          :gas_energy_tariff,
          :electricity_energy_tariff,
          switching_to_tariff: [:supplier]
        ).find(params[:id])

        # Preload associations to prevent N+1 queries
        ActiveRecord::Associations::Preloader.new(
          records: [@energy_switch],
          associations: [
            :switch_user,
            :address,
            :gas_energy_tariff,
            :electricity_energy_tariff
          ]
        ).call
      end

      def authorize_onboarding_for_switch
        authorize_onboarding(@energy_switch.switch_user_id)
      end

      def set_profile_class_and_gsp_code_for_electricity_tariff(energy_switch)
        electricity_tariff = energy_switch.electricity_energy_tariff

        if electricity_tariff.present?
          Rails.cache.fetch("energy_switch_#{energy_switch.id}_tariff_details", expires_in: 10.minutes) do
            if electricity_tariff.gsp_code.blank?
              electricity_xoserve_api = XoserveElectricityApiService.new
              result = electricity_xoserve_api.search_utility_address(electricity_tariff.meter_serial_number)

              electricity_tariff.gsp_code = result["gsp_code"]

              if electricity_tariff.profile_class.blank?
                technical_details = electricity_xoserve_api.get_technical_details_by_mpan(result["mpan"])
                electricity_tariff.profile_class = technical_details["profile_class"]
              end

              electricity_tariff.save!
            end
            
            true # Return value for cache
          end
        end
      end

      def confirm_switch_params
        params.permit(
          :firstName, :lastName, :address, :livedThreeYears, :email, 
          :phoneNumber, :dateOfBirth, :requiresSupport, :accountHolder, 
          :sortCode, :accountNumber, :isBillingAddressSame, :billingAddress, 
          :paymentMethodPreference, :switchPreference, :switch_to, :agreeToTerms
        )
      end
    end
  end
end
