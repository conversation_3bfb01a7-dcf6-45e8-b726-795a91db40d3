# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_07_20_000001) do
  create_schema "auth"
  create_schema "drizzle"
  create_schema "extensions"
  create_schema "graphql"
  create_schema "graphql_public"
  create_schema "pgbouncer"
  create_schema "realtime"
  create_schema "storage"
  create_schema "vault"

  # These are extensions that must be enabled in order to support this database
  enable_extension "hypopg"
  enable_extension "index_advisor"
  enable_extension "pg_graphql"
  enable_extension "pg_stat_statements"
  enable_extension "pgcrypto"
  enable_extension "plpgsql"
  enable_extension "supabase_vault"
  enable_extension "uuid-ossp"

  create_table "Chat", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "createdAt", precision: nil, null: false
    t.uuid "userId", null: false
    t.text "title", null: false
    t.string "visibility", default: "private", null: false
  end

  create_table "Document", primary_key: ["id", "createdAt"], force: :cascade do |t|
    t.uuid "id", default: -> { "gen_random_uuid()" }, null: false
    t.datetime "createdAt", precision: nil, null: false
    t.text "title", null: false
    t.text "content"
    t.uuid "userId", null: false
    t.string "text", default: "text", null: false
  end

  create_table "Message", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "chatId", null: false
    t.string "role", null: false
    t.json "content", null: false
    t.datetime "createdAt", precision: nil, null: false
  end

  create_table "Message_v2", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "chatId", null: false
    t.string "role", null: false
    t.json "parts", null: false
    t.json "attachments", null: false
    t.datetime "createdAt", precision: nil, null: false
  end

  create_table "Stream", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "chatId", null: false
    t.datetime "createdAt", precision: nil, null: false
  end

  create_table "Suggestion", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "documentId", null: false
    t.datetime "documentCreatedAt", precision: nil, null: false
    t.text "originalText", null: false
    t.text "suggestedText", null: false
    t.text "description"
    t.boolean "isResolved", default: false, null: false
    t.uuid "userId", null: false
    t.datetime "createdAt", precision: nil, null: false
  end

  create_table "User", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email", limit: 64, null: false
    t.string "password", limit: 64
  end

  create_table "Vote", primary_key: ["chatId", "messageId"], force: :cascade do |t|
    t.uuid "chatId", null: false
    t.uuid "messageId", null: false
    t.boolean "isUpvoted", null: false
  end

  create_table "Vote_v2", primary_key: ["chatId", "messageId"], force: :cascade do |t|
    t.uuid "chatId", null: false
    t.uuid "messageId", null: false
    t.boolean "isUpvoted", null: false
  end

  create_table "addresses", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "switch_user_id"
    t.string "postcode", null: false
    t.string "full_address", null: false
    t.string "posttown", null: false
    t.string "street"
    t.string "number"
    t.string "flat"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["switch_user_id"], name: "index_addresses_on_switch_user_id"
  end

  create_table "admins", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email"
    t.string "first_name"
    t.string "last_name"
    t.string "password_digest"
    t.string "reset_token"
    t.datetime "reset_token_expiry"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admins_on_email", unique: true
  end

  create_table "energy_switches", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "switch_user_id"
    t.uuid "gas_energy_tariff_id"
    t.uuid "electricity_energy_tariff_id"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "supplier_reference"
    t.uuid "address_id", null: false
    t.datetime "viewed_at", precision: nil
    t.boolean "lived_three_years", default: false
    t.uuid "payment_method_id"
    t.string "reference_number", null: false
    t.uuid "switching_to_tariff_id"
    t.date "switch_date"
    t.string "bill_url"
    t.json "raw_bill_json"
    t.index ["address_id"], name: "index_energy_switches_on_address_id"
    t.index ["electricity_energy_tariff_id"], name: "index_energy_switches_on_electricity_energy_tariff_id"
    t.index ["gas_energy_tariff_id"], name: "index_energy_switches_on_gas_energy_tariff_id"
    t.index ["payment_method_id"], name: "index_energy_switches_on_payment_method_id"
    t.index ["reference_number"], name: "index_energy_switches_on_reference_number", unique: true
    t.index ["status"], name: "index_energy_switches_on_status"
    t.index ["supplier_reference"], name: "index_energy_switches_on_supplier_reference"
    t.index ["switch_user_id"], name: "index_energy_switches_on_switch_user_id"
    t.index ["switching_to_tariff_id"], name: "index_energy_switches_on_switching_to_tariff_id"
  end

  create_table "energy_tariff_rates", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "region"
    t.string "region_id"
    t.string "gsp_code"
    t.decimal "standing_charge_ex_vat", precision: 10, scale: 4
    t.decimal "standing_charge_inc_vat", precision: 10, scale: 4
    t.decimal "unit_rate_ex_vat", precision: 10, scale: 4
    t.decimal "unit_rate_inc_vat", precision: 10, scale: 4
    t.decimal "day_unit_rate_ex_vat", precision: 10, scale: 4
    t.decimal "day_unit_rate_inc_vat", precision: 10, scale: 4
    t.decimal "night_unit_rate_ex_vat", precision: 10, scale: 4
    t.decimal "night_unit_rate_inc_vat", precision: 10, scale: 4
    t.decimal "weekend_unit_rate_ex_vat", precision: 10, scale: 4
    t.decimal "weekend_unit_rate_inc_vat", precision: 10, scale: 4
    t.decimal "annual_bill", precision: 10, scale: 2
    t.string "fuel_type", null: false
    t.decimal "exit_fees", precision: 10, scale: 2, default: "0.0"
    t.integer "profile_class", null: false
    t.uuid "energy_tariff_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["energy_tariff_id", "fuel_type", "gsp_code", "profile_class"], name: "idx_energy_tariff_rates_lookup"
    t.index ["energy_tariff_id", "fuel_type"], name: "idx_energy_tariff_rates_tariff_fuel"
    t.index ["energy_tariff_id"], name: "index_energy_tariff_rates_on_energy_tariff_id"
    t.index ["fuel_type", "gsp_code"], name: "idx_energy_tariff_rates_fuel_gsp"
    t.index ["fuel_type", "profile_class", "gsp_code"], name: "idx_energy_tariff_rates_fuel_profile_gsp"
    t.index ["fuel_type"], name: "index_energy_tariff_rates_on_fuel_type"
  end

  create_table "energy_tariffs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.integer "energy_type", null: false
    t.string "tariff_name", null: false
    t.float "exit_fees", default: 0.0, null: false
    t.string "payment_methods", default: [], null: false, array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "product_code"
    t.string "display_name"
    t.date "effective_from"
    t.integer "duration"
    t.uuid "supplier_id"
    t.integer "tariff_type", default: 0, null: false
    t.boolean "is_green", default: false
    t.index ["supplier_id"], name: "index_energy_tariffs_on_supplier_id"
  end

  create_table "oauth_access_grants", force: :cascade do |t|
    t.bigint "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", force: :cascade do |t|
    t.uuid "resource_owner_id"
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.string "scopes"
    t.datetime "created_at", null: false
    t.datetime "revoked_at"
    t.string "previous_refresh_token", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "payment_methods", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "switch_user_id", null: false
    t.string "payment_type", null: false
    t.decimal "estimated_monthly_payment", precision: 10, scale: 2
    t.string "account_holder_name"
    t.string "sort_code"
    t.string "account_number"
    t.boolean "billing_address_same_as_supply", default: true
    t.string "billing_address"
    t.boolean "switch_within_5_days", default: false
    t.boolean "understand_cooling_off_period", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["switch_user_id"], name: "index_payment_methods_on_switch_user_id"
  end

  create_table "security_events", force: :cascade do |t|
    t.string "event_type", null: false
    t.string "email"
    t.string "ip_address"
    t.text "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_security_events_on_created_at"
    t.index ["email", "event_type"], name: "index_security_events_on_email_and_event_type"
    t.index ["email"], name: "index_security_events_on_email"
    t.index ["event_type"], name: "index_security_events_on_event_type"
    t.index ["ip_address", "event_type"], name: "index_security_events_on_ip_address_and_event_type"
    t.index ["ip_address"], name: "index_security_events_on_ip_address"
  end

  create_table "supplier_submissions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "energy_switch_id", null: false
    t.uuid "supplier_id", null: false
    t.string "submission_type", null: false
    t.integer "status", default: 0, null: false
    t.string "supplier_reference"
    t.datetime "submitted_at"
    t.datetime "processed_at"
    t.text "raw_request"
    t.text "raw_response"
    t.text "error_message"
    t.string "rejection_reason"
    t.integer "attempt_number", default: 1, null: false
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["energy_switch_id"], name: "index_supplier_submissions_on_energy_switch_id"
    t.index ["status"], name: "index_supplier_submissions_on_status"
    t.index ["submitted_at"], name: "index_supplier_submissions_on_submitted_at"
    t.index ["supplier_id"], name: "index_supplier_submissions_on_supplier_id"
    t.index ["supplier_reference"], name: "index_supplier_submissions_on_supplier_reference"
  end

  create_table "suppliers", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "about"
    t.string "logo"
    t.decimal "trustpilot_rating", precision: 2, scale: 1
  end

  create_table "switch_users", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "email", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "title"
    t.string "phone_number"
    t.date "date_of_birth"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "requires_psr_support", default: false
    t.integer "status", default: 0, null: false
    t.index ["email"], name: "index_switch_users_on_email", unique: true
    t.index ["status"], name: "index_switch_users_on_status"
  end

  create_table "user_tariffs", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "switch_user_id"
    t.integer "energy_type", null: false
    t.string "meter_point_reference_number"
    t.string "meter_point_administration_number"
    t.string "meter_serial_number"
    t.decimal "unit_rate"
    t.decimal "standing_charge"
    t.string "supplier_name"
    t.string "payment_method"
    t.string "tariff_type"
    t.decimal "gas_estimated_annual_usage"
    t.decimal "gas_monthly_usage"
    t.decimal "electricity_est_annual_usage"
    t.decimal "electricity_monthly_usage"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.float "exit_fees"
    t.string "tariff_name"
    t.string "gsp_code"
    t.string "profile_class"
    t.decimal "electricity_est_annual_cost"
    t.decimal "gas_estimated_annual_cost"
    t.index ["meter_serial_number"], name: "index_user_tariffs_on_meter_serial_number"
    t.index ["switch_user_id"], name: "index_user_tariffs_on_switch_user_id"
  end

  create_table "xoserve_electricity_records", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "mpan_core"
    t.string "meter_serial_number"
    t.jsonb "search_utility_address_json"
    t.jsonb "get_technical_details_by_mpan_json"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["meter_serial_number"], name: "index_xoserve_electricity_records_on_meter_serial_number"
    t.index ["mpan_core"], name: "index_xoserve_electricity_records_on_mpan_core"
  end

